# 🎨 WezTerm 主题系统指南

WezTerm 配置现在支持**动态主题切换**系统，让您可以轻松在不同配色方案之间切换。

---

## 🌟 **当前可用主题**

### 1. 🌸 **Neapsix (Ros<PERSON> Pine)**
- **风格**: 温暖优雅的深色主题
- **特色**: "All natural pine, faux fur and a bit of soho vibes"
- **配色**: 以深紫色为基调，配以温暖的粉色、金色和蓝绿色
- **适合**: 长时间编程、温馨的工作环境

### 2. 🎨 **Custom (Catppuccin Mocha)**  
- **风格**: 现代化深色主题
- **特色**: 略微调整的 Catppuccin Mocha 配色
- **配色**: 深色背景配以鲜明的对比色
- **适合**: 专业开发、高对比度需求

---

## ⌨️ **快捷键操作**

### 🔄 **主题选择器**
```
Alt + M
```
- 打开主题选择对话框
- 支持模糊搜索
- 实时预览主题效果
- 自动保存选择

### ⚡ **快速切换**
```
Alt + Ctrl + M
```
- 在可用主题间循环切换
- 显示切换通知
- 立即生效

---

## 📁 **文件结构**

```
colors/
├── neapsix.lua       # Neapsix (Rosé Pine) 主题
├── custom.lua        # Custom (Catppuccin) 主题
└── [future themes]   # 未来添加的主题

utils/
└── theme-switcher.lua # 主题切换管理器

config/
└── appearance.lua    # 外观配置 (使用主题切换器)
```

---

## 🛠️ **添加新主题**

### 1️⃣ **创建主题文件**
在 `colors/` 目录下创建新的 `.lua` 文件，例如：

```lua
-- colors/my-theme.lua
local colors = {
   foreground = '#ffffff',
   background = '#000000',
   -- ... 其他配色设置
}

local window_frame = {
   active_titlebar_bg = '#000000',
   -- ... 窗口框架设置
}

return {
   colors = colors,
   window_frame = window_frame,
   window_background_opacity = 1.0, -- 可选
}
```

### 2️⃣ **注册主题**
在 `utils/theme-switcher.lua` 的 `themes` 表中添加：

```lua
{
   name = 'my-theme',
   display_name = '🎯 我的主题',
   module = 'colors.my-theme',
   description = '主题描述'
}
```

### 3️⃣ **重启 WezTerm**
新主题将出现在主题选择器中。

---

## 🎯 **主题配置详解**

### 🎨 **基本配色**
```lua
colors = {
   foreground = '#颜色',    -- 前景文字颜色
   background = '#颜色',    -- 背景颜色
   cursor_bg = '#颜色',     -- 光标背景
   cursor_border = '#颜色', -- 光标边框
   selection_bg = '#颜色',  -- 选中背景
   selection_fg = '#颜色',  -- 选中文字
   -- ...
}
```

### 📊 **标签栏配色**
```lua
tab_bar = {
   background = 'rgba(r, g, b, a)',
   active_tab = {
      bg_color = '#颜色',
      fg_color = '#颜色',
   },
   inactive_tab = {
      bg_color = '#颜色',
      fg_color = '#颜色',
   },
   -- ...
}
```

### 🖼️ **窗口框架**
```lua
window_frame = {
   active_titlebar_bg = '#颜色',
   inactive_titlebar_bg = '#颜色',
}
```

---

## 💡 **使用技巧**

### 🌓 **主题选择建议**
- **白天工作**: Neapsix 温暖舒适
- **夜间编程**: Custom 高对比度
- **演示场合**: 根据投影效果选择

### 🔧 **自定义优化**
- 调整 `window_background_opacity` 设置透明度
- 修改 `inactive_pane_hsb` 调整非活动窗格显示
- 配合背景图片使用 (`Alt + B`)

### ⚡ **性能提示**
- 主题切换会触发配置重载
- 切换后稍等片刻让配置生效
- 复杂主题可能影响渲染性能

---

## 🚀 **快速开始**

1. **尝试 Neapsix 主题**:
   ```
   按 Alt + M → 选择 "🌸 Neapsix (Rosé Pine)"
   ```

2. **快速切换体验**:
   ```
   按 Alt + Ctrl + M → 在主题间循环
   ```

3. **配合背景图片**:
   ```
   按 Alt + B → 开启背景图片
   按 Alt + ? → 选择特定背景
   ```

---

## 🆘 **故障排除**

### ❌ **主题切换失败**
- 检查主题文件语法是否正确
- 确认 `current_theme.txt` 文件权限
- 重启 WezTerm 重新加载配置

### 🎨 **颜色显示异常**
- 确认终端支持真彩色
- 检查主题文件中的颜色格式
- 验证 WebGpu 前端是否正常

### 📱 **切换器不显示**
- 确认快捷键没有冲突
- 检查 theme-switcher.lua 是否正确加载
- 查看调试信息 (F12)

---

## 🔮 **未来功能**

- [ ] 自动主题切换 (跟随系统深色/浅色模式)
- [ ] 主题导入/导出功能  
- [ ] 在线主题库支持
- [ ] 实时主题编辑器
- [ ] 主题同步到云端

---

> 💡 **提示**: 使用 `Alt + M` 体验不同主题，找到最适合您工作流程的配色方案！ 
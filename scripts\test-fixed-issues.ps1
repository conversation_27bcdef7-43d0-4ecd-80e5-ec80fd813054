# 🔧 WezTerm 问题修复验证脚本
Write-Host "🔧 WezTerm 问题修复验证" -ForegroundColor Cyan
Write-Host "=======================" -ForegroundColor Cyan
Write-Host ""

# 1. 检查 resurrect 状态目录结构
Write-Host "📁 检查 resurrect 状态目录..." -ForegroundColor Yellow
$resurrectDir = "$env:LOCALAPPDATA\wezterm\resurrect_states"
$subDirs = @("workspace", "window", "tab")

foreach ($subDir in $subDirs) {
    $fullPath = Join-Path $resurrectDir $subDir
    if (Test-Path $fullPath) {
        Write-Host "✅ $subDir 目录存在: $fullPath" -ForegroundColor Green
    } else {
        Write-Host "❌ $subDir 目录不存在" -ForegroundColor Red
        New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
        Write-Host "✅ 已创建 $subDir 目录" -ForegroundColor Green
    }
}

# 2. 检查目录权限
Write-Host "🔒 检查目录权限..." -ForegroundColor Yellow
try {
    $testFile = Join-Path $resurrectDir "test_write.txt"
    "test" | Out-File -FilePath $testFile -Force
    Remove-Item $testFile -Force
    Write-Host "✅ 目录写入权限正常" -ForegroundColor Green
} catch {
    Write-Host "❌ 目录写入权限有问题: $($_.Exception.Message)" -ForegroundColor Red
}

# 3. 验证配置文件语法
Write-Host "📝 验证配置文件..." -ForegroundColor Yellow

# 检查 tab-colors.lua
$tabColorsFile = "events\tab-colors.lua"
if (Test-Path $tabColorsFile) {
    $content = Get-Content $tabColorsFile -Raw
    if ($content -match "get_foreground_process_name") {
        Write-Host "❌ tab-colors.lua 仍包含过时的 API" -ForegroundColor Red
    } else {
        Write-Host "✅ tab-colors.lua API 已更新" -ForegroundColor Green
    }
} else {
    Write-Host "❌ tab-colors.lua 文件不存在" -ForegroundColor Red
}

# 4. 检查项目路径配置
Write-Host "📂 检查项目路径配置..." -ForegroundColor Yellow
$configFiles = @("wezterm.lua", "test-plugins.lua")

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        $content = Get-Content $file -Raw
        if ($content -match 'wezterm\.home_dir \.\. "\\\\Documents"') {
            Write-Host "✅ $file 路径分隔符已修复" -ForegroundColor Green
        } else {
            Write-Host "⚠️ $file 路径分隔符可能需要检查" -ForegroundColor Yellow
        }
    }
}

# 5. 测试插件加载
Write-Host "🔌 测试插件配置..." -ForegroundColor Yellow
try {
    $testOutput = & wezterm --config-file test-plugins.lua --help 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 插件配置语法正确" -ForegroundColor Green
    } else {
        Write-Host "❌ 插件配置有语法错误" -ForegroundColor Red
        Write-Host "错误输出: $testOutput" -ForegroundColor Gray
    }
} catch {
    Write-Host "⚠️ 无法测试插件配置: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 6. 检查 resurrect 状态文件权限（新增）
Write-Host "📋 检查 resurrect 状态文件权限..." -ForegroundColor Yellow
$workspaceDir = Join-Path $resurrectDir "workspace"
if (Test-Path $workspaceDir) {
    try {
        $testStateFile = Join-Path $workspaceDir "test.json"
        '{"test": "data"}' | Out-File -FilePath $testStateFile -Force -Encoding UTF8
        Remove-Item $testStateFile -Force
        Write-Host "✅ workspace 目录可以写入状态文件" -ForegroundColor Green
    } catch {
        Write-Host "❌ workspace 目录无法写入: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ workspace 目录不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "🎯 修复总结:" -ForegroundColor Cyan
Write-Host "1. ✅ 修复了 tab-colors.lua 中的过时 API 调用" -ForegroundColor Green
Write-Host "2. ✅ 创建了完整的 resurrect 状态目录结构" -ForegroundColor Green
Write-Host "3. ✅ 修复了 Windows 路径分隔符问题" -ForegroundColor Green
Write-Host "4. ✅ 验证了目录写入权限" -ForegroundColor Green
Write-Host "5. ✅ 修复了 PowerShell 脚本警告" -ForegroundColor Green

Write-Host ""
Write-Host "🚀 下一步:" -ForegroundColor Cyan
Write-Host "1. 重启 WezTerm 测试修复效果" -ForegroundColor White
Write-Host "2. 运行: wezterm --config-file test-plugins.lua" -ForegroundColor White
Write-Host "3. 检查是否还有错误信息" -ForegroundColor White

Write-Host ""
Write-Host "💡 如果仍有问题，请查看 WezTerm 日志:" -ForegroundColor Yellow
Write-Host "   按 F12 打开调试控制台查看详细错误信息" -ForegroundColor White 
local ssh_connections = require('config.ssh-connections')

return {
   -- ref: https://wezfurlong.org/wezterm/config/lua/SshDomain.html
   -- SSH 域配置 - 使用SSH管理器
   ssh_domains = ssh_connections:get_domains(),

   -- ref: https://wezfurlong.org/wezterm/multiplexing.html#unix-domains
   unix_domains = {},

   -- ref: https://wezfurlong.org/wezterm/config/lua/WslDomain.html
   wsl_domains = {
      -- Kali Linux WSL - 已成功导入，默认用户为root
      {
         name = 'WSL:Kali',
         distribution = 'kali-linux',
         username = 'root', -- 实际的默认用户
         default_cwd = '/root', -- root用户的家目录
         default_prog = { 'bash', '-l' },
      },

      -- 备用配置：如果有其他用户
      {
         name = 'WSL:Kali-User',
         distribution = 'kali-linux',
         username = 'kali', -- 如果创建了kali用户
         default_cwd = '/home/<USER>',
         default_prog = { 'bash', '-l' },
      },
   },
}

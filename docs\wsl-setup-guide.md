# 🐧 WSL Kali Linux 设置指南

## 📍 **您的自定义安装路径**
- **安装位置**: `F:\wsl\kali_root`
- **系统**: Kali Linux WSL

---

## 🔧 **WSL注册和配置步骤**

### 1️⃣ **检查当前WSL状态**
```powershell
# 检查已安装的WSL发行版
wsl --list --verbose

# 检查WSL版本
wsl --version
```

### 2️⃣ **注册自定义WSL发行版**
```powershell
# 方法1: 如果您有Kali Linux的tar文件
wsl --import kali-linux F:\wsl\kali_root path\to\kali-linux.tar

# 方法2: 如果从现有安装导入
wsl --import kali-linux F:\wsl\kali_root F:\wsl\backup\kali-backup.tar

# 方法3: 如果需要从头安装
# 下载Kali Linux WSL从微软商店或官方源
```

### 3️⃣ **验证注册结果**
```powershell
# 验证发行版已注册
wsl --list --verbose

# 应该看到类似输出：
# NAME          STATE           VERSION
# kali-linux    Stopped         2
```

---

## 🛠️ **WezTerm配置调整**

### 📝 **发行版名称确认**
根据您的实际WSL注册名称，可能需要调整 `config/domains.lua` 中的配置：

```lua
-- 常见的Kali Linux WSL发行版名称：
distribution = 'kali-linux'     -- 最常见
-- 或者
distribution = 'Kali'           -- 简化名称
-- 或者
distribution = 'KaliLinux'      -- 商店版本
```

### 🔍 **查找正确的发行版名称**
1. 运行 `wsl --list` 查看确切名称
2. 在WezTerm中测试连接
3. 根据错误信息调整配置

---

## 🚀 **快速启动测试**

### 1️⃣ **命令行测试**
```powershell
# 直接启动WSL
wsl -d kali-linux

# 或者使用默认发行版
wsl
```

### 2️⃣ **WezTerm中测试**
1. 重启WezTerm
2. 按 `Alt + 3` 尝试启动WSL Kali
3. 或按 `F3` 在启动器中选择 "WSL:Kali"

---

## 🔧 **常见问题排查**

### ❌ **问题1: "distribution not found"**
**解决方案**:
1. 检查发行版名称：`wsl --list`
2. 更新 `config/domains.lua` 中的 `distribution` 字段
3. 重启WezTerm

### ❌ **问题2: 用户名不正确**
**解决方案**:
```powershell
# 检查默认用户
wsl -d kali-linux whoami

# 设置默认用户 (如果需要)
wsl -d kali-linux -u root passwd kali
```

### ❌ **问题3: 路径问题**
**解决方案**:
```bash
# 在WSL中检查用户目录
ls -la /home/
ls -la /root/

# 确认正确的家目录路径
echo $HOME
```

---

## 🎯 **推荐配置**

### 🔷 **基本配置** (适用于大多数情况)
```lua
{
   name = 'WSL:Kali',
   distribution = 'kali-linux',
   username = 'kali',
   default_cwd = '/home/<USER>',
   default_prog = { 'bash', '-l' },
}
```

### ⚫ **Root用户配置** (如果使用root)
```lua
{
   name = 'WSL:Kali-Root',
   distribution = 'kali-linux',
   username = 'root',
   default_cwd = '/root',
   default_prog = { 'bash', '-l' },
}
```

### 🛡️ **安全增强配置**
```lua
{
   name = 'WSL:Kali',
   distribution = 'kali-linux',
   username = 'kali',
   default_cwd = '/home/<USER>',
   default_prog = { 'zsh', '-l' }, -- 如果使用zsh
   -- wsl_version = 2, -- 确保使用WSL2
}
```

---

## 📋 **设置检查清单**

- [ ] WSL2已安装并启用
- [ ] Kali Linux已下载到 `F:\wsl\kali_root`
- [ ] WSL发行版已正确注册
- [ ] `wsl --list` 显示Kali Linux
- [ ] WezTerm配置已更新发行版名称
- [ ] 用户名和路径配置正确
- [ ] WezTerm重启后可以启动WSL

---

## 🚀 **高级设置选项**

### 🔧 **性能优化**
```lua
-- 在domains.lua中添加性能选项
{
   name = 'WSL:Kali',
   distribution = 'kali-linux',
   username = 'kali',
   default_cwd = '/home/<USER>',
   default_prog = { 'bash', '-l' },
   
   -- 性能选项
   skip_pty = false,
   local_echo_threshold_ms = 10,
}
```

### 🎨 **环境变量设置**
```bash
# 在WSL中的 ~/.bashrc 或 ~/.zshrc 添加：
export DISPLAY=:0.0
export PULSE_SERVER=tcp:$(grep nameserver /etc/resolv.conf | awk '{print $2}'):4713
```

---

## 💡 **使用提示**

1. **第一次启动可能较慢** - WSL需要初始化
2. **使用 `Alt + 3`** - 快速启动Kali Linux
3. **工作目录同步** - 使用 `Alt + Ctrl + C` 复制Windows路径，然后在WSL中导航
4. **文件共享** - Windows文件系统挂载在 `/mnt/c/`, `/mnt/f/` 等
5. **网络访问** - WSL2具有独立的IP地址

---

## 🆘 **获取帮助**

如果遇到问题：
1. 检查WSL状态：`wsl --status`
2. 查看WezTerm日志：按 `F12` 查看调试信息
3. 重置WSL网络：`wsl --shutdown` 然后重启
4. 参考官方文档：[WezTerm WSL配置](https://wezfurlong.org/wezterm/config/lua/WslDomain.html)

---

> 💡 **提示**: 设置完成后，您就可以通过 `Alt + 3` 快速启动Kali Linux，享受无缝的Windows-Linux开发环境！ 
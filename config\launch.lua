local platform = require('utils.platform')
local act = require('wezterm').action

local options = {
   default_prog = {},
   launch_menu = {},
}

if platform.is_win then
   -- 设置默认终端为 PowerShell 7 (pwsh)
   options.default_prog = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' }
   
   options.launch_menu = {
      -- PowerShell 相关
      { 
         label = '🔷 PowerShell 7', 
         args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' } 
      },
      { 
         label = '🔵 PowerShell (Legacy)', 
         args = { 'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe', '-NoLogo' } 
      },
      
      -- Git Bash
      { 
         label = '🐙 Git Bash', 
         args = { 'D:\\Git\\bin\\bash.exe', '-l' },
         cwd = os.getenv('USERPROFILE')
      },
      
      -- WSL Kali Linux (域配置)
      {
         label = '🐧 WSL Kali (域)',
         domain = { DomainName = 'WSL:Kali' },
      },
      
      -- WSL Kali Linux (直接命令) - 备用方法
      {
         label = '🐧 WSL Kali (直接)',
         args = { 'wsl.exe', '-d', 'kali-linux' },
      },
      
      -- WSL Kali Linux (通用) - 如果没有指定发行版名称
      {
         label = '🐧 WSL Kali (通用)',
         args = { 'wsl.exe' },
      },
      
      -- 系统终端
      { 
         label = '⚫ Command Prompt', 
         args = { 'cmd.exe' } 
      },
      
      -- SSH 连接模板
      { 
         label = '🌐 SSH Connection', 
         args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo', '-Command', 'Write-Host "使用 ssh user@hostname 连接远程服务器"' }
      },
      
      -- 开发相关
      { 
         label = '⚡ Node.js REPL', 
         args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo', '-Command', 'node' }
      },
      
      -- 其他可选终端 (如果安装了)
      { 
         label = '🐚 Nushell', 
         args = { 'nu' } 
      },
   }
elseif platform.is_mac then
   options.default_prog = { '/opt/homebrew/bin/fish', '-l' }
   options.launch_menu = {
      { label = 'Bash', args = { 'bash', '-l' } },
      { label = 'Fish', args = { '/opt/homebrew/bin/fish', '-l' } },
      { label = 'Nushell', args = { '/opt/homebrew/bin/nu', '-l' } },
      { label = 'Zsh', args = { 'zsh', '-l' } },
   }
elseif platform.is_linux then
   options.default_prog = { 'fish', '-l' }
   options.launch_menu = {
      { label = 'Bash', args = { 'bash', '-l' } },
      { label = 'Fish', args = { 'fish', '-l' } },
      { label = 'Zsh', args = { 'zsh', '-l' } },
   }
end

return options

local wezterm = require('wezterm')

---@class SSHManager
---@field connections table[] SSH连接配置列表
local SSHManager = {}
SSHManager.__index = SSHManager

--- 初始化SSH管理器
---@return SSHManager
function SSHManager:new()
    local instance = {
        connections = {}
    }
    setmetatable(instance, self)
    return instance
end

--- 添加SSH连接配置
---@param config table SSH连接配置
---@return SSHManager
function SSHManager:add_connection(config)
    table.insert(self.connections, config)
    return self
end

--- 添加快速SSH连接（简化配置）
---@param name string 连接名称
---@param host string 主机地址
---@param username string 用户名
---@param port number? 端口号（默认22）
---@param key_file string? 私钥文件路径
---@return SSHManager
function SSHManager:add_quick_connection(name, host, username, port, key_file)
    local config = {
        name = name,
        remote_address = host,
        username = username,
        multiplexing = 'None',
        default_prog = { 'bash', '-l' },
        assume_shell = 'Posix',
    }
    
    if port or key_file then
        config.ssh_option = {}
        if port then
            config.ssh_option.Port = tostring(port)
        end
        if key_file then
            config.ssh_option.IdentityFile = key_file
        end
    end
    
    return self:add_connection(config)
end

--- 生成SSH域配置
---@return table[]
function SSHManager:get_domains()
    return self.connections
end

--- 生成启动菜单项
---@return table[]
function SSHManager:get_launch_menu_items()
    local items = {}
    
    for _, conn in ipairs(self.connections) do
        table.insert(items, {
            label = '🌐 SSH: ' .. conn.name,
            args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo', '-Command', 
                    string.format('ssh %s@%s', conn.username, conn.remote_address) }
        })
    end
    
    return items
end

--- 创建SSH快速连接对话框
---@param window any WezTerm窗口对象
function SSHManager:show_connection_dialog(window)
    local choices = {}
    
    -- 添加预定义连接
    for i, conn in ipairs(self.connections) do
        table.insert(choices, {
            id = tostring(i),
            label = string.format('%s (%s@%s)', conn.name, conn.username, conn.remote_address)
        })
    end
    
    -- 添加自定义连接选项
    table.insert(choices, {
        id = 'custom',
        label = '🔧 自定义SSH连接...'
    })
    
    window:perform_action(
        wezterm.action.InputSelector({
            title = 'SSH连接选择',
            choices = choices,
            fuzzy = true,
            fuzzy_description = '选择SSH连接: ',
            action = wezterm.action_callback(function(child_window, pane, id, label)
                if not id then
                    return
                end
                
                if id == 'custom' then
                    -- 显示自定义连接输入
                    child_window:perform_action(
                        wezterm.action.PromptInputLine({
                            description = '输入SSH连接 (格式: user@host[:port]): ',
                            action = wezterm.action_callback(function(input_window, input_pane, line)
                                if line then
                                    input_window:perform_action(
                                        wezterm.action.SpawnCommandInNewTab({
                                            args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo', '-Command', 'ssh ' .. line }
                                        }),
                                        input_pane
                                    )
                                end
                            end)
                        }),
                        pane
                    )
                else
                    -- 使用预定义连接
                    local conn = self.connections[tonumber(id)]
                    if conn then
                        child_window:perform_action(
                            wezterm.action.SpawnCommandInNewTab({
                                args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo', '-Command', 
                                        string.format('ssh %s@%s', conn.username, conn.remote_address) }
                            }),
                            pane
                        )
                    end
                end
            end)
        }),
        window:active_pane()
    )
end

return SSHManager 
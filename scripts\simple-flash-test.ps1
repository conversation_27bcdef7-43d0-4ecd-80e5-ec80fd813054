#!/usr/bin/env pwsh

Write-Host "🔧 WezTerm 闪动问题修复验证" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 测试主配置文件
Write-Host "测试主配置文件..." -ForegroundColor Yellow
$result1 = & wezterm --config-file wezterm.lua --help
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ wezterm.lua: 语法正确" -ForegroundColor Green
} else {
    Write-Host "❌ wezterm.lua: 语法错误" -ForegroundColor Red
    Write-Host "错误详情: $result1" -ForegroundColor Red
}

# 测试修复配置文件
Write-Host "测试修复配置文件..." -ForegroundColor Yellow
$result2 = & wezterm --config-file wezterm-fixed.lua --help
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ wezterm-fixed.lua: 语法正确" -ForegroundColor Green
} else {
    Write-Host "❌ wezterm-fixed.lua: 语法错误" -ForegroundColor Red
    Write-Host "错误详情: $result2" -ForegroundColor Red
}

# 检查目录
Write-Host "检查Resurrect目录..." -ForegroundColor Yellow
$dir = "$env:LOCALAPPDATA\wezterm\resurrect_states\workspace\default.json"
if (Test-Path $dir) {
    Write-Host "✅ default.json 存在" -ForegroundColor Green
} else {
    Write-Host "❌ default.json 不存在" -ForegroundColor Red
}

Write-Host ""
Write-Host "修复总结:" -ForegroundColor Cyan
Write-Host "✅ 移除重复事件监听器"
Write-Host "✅ 添加插件重复加载保护"
Write-Host "✅ 延长自动保存间隔"
Write-Host "✅ 简化事件处理"

Write-Host ""
Write-Host "建议:" -ForegroundColor Yellow
Write-Host "1. 关闭所有 WezTerm 窗口"
Write-Host "2. 启动: wezterm"
Write-Host "3. 观察是否还有闪动"

Write-Host ""
Write-Host "✅ 验证完成!" -ForegroundColor Green 
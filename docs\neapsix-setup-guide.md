# 🌸 Neapsix 主题设置完成指南

恭喜！您的 WezTerm 现在已经成功配置了美丽的 **Neapsix (<PERSON><PERSON><PERSON> Pine)** 主题！

---

## ✅ **配置完成状态**

### 🎨 **主题系统**
- ✅ Neapsix (<PERSON><PERSON><PERSON>) 主题已安装
- ✅ 主题切换器已配置
- ✅ 快捷键已设置
- ✅ 默认主题设为 Neapsix

### 📁 **新增文件**
```
colors/
└── neapsix.lua           # Neapsix 主题配色

utils/
└── theme-switcher.lua    # 主题切换管理器

docs/
├── theme-guide.md        # 主题使用指南
├── neapsix-setup-guide.md # 本文档
└── keybindings-reference.md # 更新的快捷键参考

current_theme.txt         # 当前主题配置
test-neapsix-theme.lua    # 测试脚本
```

### 🔧 **修改的文件**- `config/appearance.lua` - 使用主题切换器，标签栏移至底部，标签栏移至底部- `config/bindings.lua` - 添加主题切换快捷键

---

## 🚀 **立即使用**

### 1️⃣ **重启 WezTerm**
关闭并重新打开 WezTerm，您将看到美丽的 Neapsix 主题！

### 2️⃣ **主题切换**
```bash
# 打开主题选择器
Alt + M

# 快速切换主题
Alt + Ctrl + M
```

### 3️⃣ **验证配置**
运行测试脚本确认一切正常：
```bash
wezterm --config-file test-neapsix-theme.lua
```

---

## 🎨 **Neapsix 主题特色**

### 🌈 **配色方案**
- **背景色**: `#191724` (深紫色)
- **前景色**: `#e0def4` (淡紫白)
- **光标色**: `#ebbcba` (温暖玫瑰)
- **选中色**: `#403d52` (中等紫)

### 🎯 **设计理念**
> "All natural pine, faux fur and a bit of soho vibes for the classy minimalist"

- **温暖优雅**: 深紫色基调营造温馨氛围
- **护眼舒适**: 柔和对比度适合长时间使用
- **细节丰富**: 完整的标签栏、窗格、选择配色
- **专业美观**: 适合开发和日常使用

---

## ⌨️ **新增快捷键**

| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Alt + M` | **主题选择器** | 在 Neapsix 和 Custom 间选择 |
| `Alt + Ctrl + M` | **快速切换** | 循环切换可用主题 |

---

## 🛠️ **自定义选项**

### 🔧 **调整透明度**
编辑 `colors/neapsix.lua`：
```lua
return {
   colors = colors,
   window_frame = window_frame,
   window_background_opacity = 0.90, -- 调整此值 (0.0-1.0)
}
```

### 🎨 **修改配色**
在 `colors/neapsix.lua` 中调整 `palette` 表中的颜色值。

### ➕ **添加新主题**
1. 在 `colors/` 目录创建新的 `.lua` 文件
2. 在 `utils/theme-switcher.lua` 的 `themes` 表中注册
3. 重启 WezTerm

---

## 🔍 **故障排除**

### ❌ **主题没有生效**
1. 确认 WezTerm 已重启
2. 检查 `current_theme.txt` 内容是否为 `neapsix`
3. 运行测试脚本验证配置

### 🎨 **颜色显示异常**
1. 确认终端支持真彩色
2. 检查 WebGpu 前端是否启用
3. 验证显卡驱动是否最新

### ⌨️ **快捷键不工作**
1. 确认没有与其他软件冲突
2. 检查 `config/bindings.lua` 是否正确加载
3. 尝试使用 F12 查看调试信息

---

## 🌟 **推荐配置**

### 🖼️ **配合背景图片**
```bash
Alt + B    # 开启背景模式
Alt + ?    # 选择背景图片
```

### 🔤 **字体建议**
推荐使用等宽字体如：
- Fira Code
- JetBrains Mono
- Cascadia Code

### 🎛️ **窗格布局**
```bash
Alt + \         # 垂直分割
Alt + Ctrl + \  # 水平分割
Alt + E         # 平衡窗格
```

---

## 🎉 **享受您的新主题！**

现在您拥有了一个美丽、功能完整的 WezTerm 配置：

- 🌸 **Neapsix 主题**: 温暖优雅的配色
- ⚡ **快速切换**: 随时更换主题
- 🚀 **完整功能**: 多终端、SSH、背景等
- 📚 **详细文档**: 完整的使用指南

### 📖 **相关文档**
- [主题系统指南](theme-guide.md)
- [快捷键参考](keybindings-reference.md)
- [SSH 连接指南](ssh-guide.md)

---

> 💡 **提示**: 使用 `Alt + M` 随时切换主题，体验不同的视觉风格！ 
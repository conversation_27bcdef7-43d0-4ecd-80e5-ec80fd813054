# Simple WezTerm Fix Verification
Write-Host "WezTerm Fix Verification" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

# Check resurrect directories
Write-Host "Checking resurrect directories..." -ForegroundColor Yellow
$dirs = @(
    "$env:LOCALAPPDATA\wezterm\resurrect_states\workspace",
    "$env:LOCALAPPDATA\wezterm\resurrect_states\window", 
    "$env:LOCALAPPDATA\wezterm\resurrect_states\tab"
)

foreach ($dir in $dirs) {
    if (Test-Path $dir) {
        Write-Host "✅ Directory exists: $dir" -ForegroundColor Green
    } else {
        Write-Host "❌ Directory missing: $dir" -ForegroundColor Red
    }
}

# Check API fixes in tab-colors.lua
Write-Host "Checking tab-colors.lua API fixes..." -ForegroundColor Yellow
if (Test-Path "events\tab-colors.lua") {
    $content = Get-Content "events\tab-colors.lua" -Raw
    if ($content -match "get_foreground_process_info") {
        Write-Host "✅ tab-colors.lua uses new API" -ForegroundColor Green
    } else {
        Write-Host "❌ tab-colors.lua needs API update" -ForegroundColor Red
    }
} else {
    Write-Host "❌ tab-colors.lua not found" -ForegroundColor Red
}

# Check path separators
Write-Host "Checking Windows path separators..." -ForegroundColor Yellow
if (Test-Path "wezterm.lua") {
    $content = Get-Content "wezterm.lua" -Raw
    if ($content -match "\\\\Documents") {
        Write-Host "✅ wezterm.lua uses Windows paths" -ForegroundColor Green
    } else {
        Write-Host "⚠️ wezterm.lua path separators need review" -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Summary of fixes applied:" -ForegroundColor Cyan
Write-Host "1. Updated tab-colors.lua API calls" -ForegroundColor Green
Write-Host "2. Created resurrect state directories" -ForegroundColor Green  
Write-Host "3. Fixed Windows path separators" -ForegroundColor Green

Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Restart WezTerm to test fixes" -ForegroundColor White
Write-Host "2. Run: wezterm --config-file test-plugins.lua" -ForegroundColor White 
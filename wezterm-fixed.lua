local Config = require('config')
local wezterm = require('wezterm')

-- 🚩 控制插件只加载一次的标志
local plugins_have_been_loaded = false

-- 基础UI配置
require('utils.backdrops')
   :set_images()
   :random()

require('events.left-status').setup()
require('events.right-status').setup({ date_format = '%a %H:%M:%S' })
require('events.new-tab-button').setup()
require('events.tab-colors').setup()

local config = Config:init()
   :append(require('config.appearance'))
   :append(require('config.bindings'))
   :append(require('config.domains'))
   :append(require('config.fonts'))
   :append(require('config.general'))
   :append(require('config.launch'))

-- 🎨 添加插件配置标志以避免重复加载
if not plugins_have_been_loaded then
   -- 🔌 加载推荐插件 (只加载一次)
   local cmd_sender = wezterm.plugin.require("https://github.com/aureolebigben/wezterm-cmd-sender")
   local resurrect = wezterm.plugin.require("https://github.com/MLFlexer/resurrect.wezterm")
   local workspace_switcher = wezterm.plugin.require("https://github.com/MLFlexer/smart_workspace_switcher.wezterm")
   local sessionizer = wezterm.plugin.require("https://github.com/ElCapitanSponge/sessionizer.wezterm")

   -- 配置 sessionizer 项目目录 (Windows路径)
   local username = os.getenv("USERNAME") or "User"
   local projects = {
       wezterm.home_dir .. "\\Documents",
       wezterm.home_dir .. "\\Desktop", 
       "D:\\Projects",
       "D:\\Git",
       "C:\\Users\\<USER>\\source\\repos"
   }
   sessionizer.set_projects(projects)

   -- 🔧 应用插件配置
   cmd_sender.apply_to_config(config, {
      key = 'b',
      mods = 'ALT|CTRL',
      description = '向所有窗格发送命令'
   })

   sessionizer.configure(config)
   workspace_switcher.apply_to_config(config)

   -- 📁 设置 Windows 状态目录
   resurrect.state_manager.change_state_save_dir("C:\\Users\\<USER>\\AppData\\Local\\wezterm\\resurrect_states\\")

   -- 🕒 启用自动保存会话 (延长间隔以减少频繁操作)
   resurrect.state_manager.periodic_save({
      interval_seconds = 30 * 60,  -- 30分钟，减少频繁保存
      save_workspaces = true,
      save_windows = true,
      save_tabs = true,
   })

   -- 🚀 启动时恢复会话
   wezterm.on("gui-startup", resurrect.state_manager.resurrect_on_gui_startup)

   -- 📊 工作区切换时的状态更新 (简化版)
   wezterm.on("smart_workspace_switcher.workspace_switcher.chosen", function(window, workspace)
      local gui_win = window:gui_window()
      local base_path = string.gsub(workspace, "(.*[/\\])(.*)", "%2")
      gui_win:set_right_status(wezterm.format({
         { Foreground = { Color = "#c4a7e7" } },
         { Text = "📁 " .. base_path .. "  " },
      }))
   end)

   -- 标记插件已加载
   plugins_have_been_loaded = true
   
   wezterm.log_info('✅ 所有插件已成功加载，无重复加载')
end

return config.options 
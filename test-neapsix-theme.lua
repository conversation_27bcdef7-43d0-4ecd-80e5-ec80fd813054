-- 测试 Neapsix 主题配置
local wezterm = require('wezterm')

-- 测试主题切换器
local theme_switcher = require('utils.theme-switcher')

print("🎨 测试 Neapsix 主题配置...")

-- 测试主题列表
print("\n📋 可用主题:")
for i, theme in ipairs(theme_switcher.themes) do
   print(string.format("  %d. %s - %s", i, theme.display_name, theme.description))
end

-- 测试当前主题
local current_theme = theme_switcher:get_current_theme()
print(string.format("\n🌟 当前主题: %s", current_theme))

-- 测试主题配置加载
local theme_config = theme_switcher:get_theme_config(current_theme)
print(string.format("\n✅ 主题配置加载成功"))
print(string.format("   - 前景色: %s", theme_config.colors.foreground))
print(string.format("   - 背景色: %s", theme_config.colors.background))
print(string.format("   - 光标色: %s", theme_config.colors.cursor_bg))
print(string.format("   - 透明度: %.2f", theme_config.window_background_opacity or 1.0))

-- 测试 neapsix 主题直接加载
local neapsix = require('colors.neapsix')
print(string.format("\n🌸 Neapsix 主题验证:"))
print(string.format("   - 基础色: %s", neapsix.colors.background))
print(string.format("   - 文字色: %s", neapsix.colors.foreground))
print(string.format("   - 玫瑰色: %s", neapsix.colors.cursor_bg))

print("\n🎉 所有测试通过！Neapsix 主题配置正常！")
print("\n💡 使用方法:")
print("   - Alt + M: 打开主题选择器")
print("   - Alt + Ctrl + M: 快速切换主题")
print("   - 重启 WezTerm 以应用新配置")

return {
   test_passed = true,
   current_theme = current_theme,
   theme_config = theme_config
} 
# WezTerm Plugin Verification Script
Write-Host "WezTerm Plugin Installation Verification" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Check zoxide installation
Write-Host "Checking zoxide..." -ForegroundColor Yellow
if (Get-Command zoxide -ErrorAction SilentlyContinue) {
    $zoxideVersion = zoxide --version
    Write-Host "✅ zoxide installed: $zoxideVersion" -ForegroundColor Green
} else {
    Write-Host "❌ zoxide not installed" -ForegroundColor Red
}

# Check plugin directory
Write-Host "Checking plugin directory..." -ForegroundColor Yellow
$pluginDir = "$env:APPDATA\wezterm\plugins"
if (Test-Path $pluginDir) {
    Write-Host "✅ Plugin directory exists: $pluginDir" -ForegroundColor Green
    $plugins = Get-ChildItem $pluginDir -Directory
    Write-Host "Installed plugins:" -ForegroundColor Cyan
    foreach ($plugin in $plugins) {
        Write-Host "  - $($plugin.Name)" -ForegroundColor White
    }
} else {
    Write-Host "❌ Plugin directory does not exist" -ForegroundColor Red
}

# Check resurrect state directory
Write-Host "Checking resurrect state directory..." -ForegroundColor Yellow
$resurrectDir = "$env:LOCALAPPDATA\wezterm\resurrect_states"
if (Test-Path $resurrectDir) {
    Write-Host "✅ Resurrect state directory exists: $resurrectDir" -ForegroundColor Green
} else {
    Write-Host "❌ Resurrect state directory does not exist" -ForegroundColor Red
}

# Check configuration files
Write-Host "Checking configuration files..." -ForegroundColor Yellow
$configFiles = @(
    "wezterm.lua",
    "test-plugins.lua",
    "config/bindings.lua",
    "docs/recommended-plugins-guide.md"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Write-Host "✅ $file exists" -ForegroundColor Green
    } else {
        Write-Host "❌ $file does not exist" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "Quick test command:" -ForegroundColor Cyan
Write-Host "  wezterm --config-file test-plugins.lua" -ForegroundColor White
Write-Host ""
Write-Host "Plugin shortcuts:" -ForegroundColor Cyan
Write-Host "  Alt+Ctrl+B - Batch command sender" -ForegroundColor White
Write-Host "  Leader+R   - Restore session" -ForegroundColor White
Write-Host "  Leader+S   - Save session" -ForegroundColor White
Write-Host "  Alt+A      - Workspace switcher" -ForegroundColor White
Write-Host "  Leader+F   - Project switcher" -ForegroundColor White 
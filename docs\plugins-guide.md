# 🔌 WezTerm 插件管理指南

您的 WezTerm 配置现在集成了强大的插件系统！插件可以扩展终端的功能，让您的工作流更加高效。

---

## 📦 **已安装的插件**

### 🚀 **wezterm-cmd-sender**
**作者**: aureolebigben  
**功能**: 向当前标签页的所有窗格发送相同命令  
**快捷键**: `Alt + Ctrl + B`

#### ✨ **使用场景**
- **批量部署**: 同时在多个服务器上执行相同的部署命令
- **日志监控**: 在多个窗格中同时启动日志监控
- **集群管理**: 对多个节点执行相同的管理操作
- **开发测试**: 在多个环境中同时运行测试

#### 🎯 **使用方法**
1. 先创建多个窗格（使用 `Alt + \` 或 `Alt + Ctrl + \`）
2. 按 `Alt + Ctrl + B` 
3. 在弹出的输入框中输入要发送的命令
4. 按回车，命令会同时发送到所有窗格

#### 💡 **实用示例**
```bash
# 批量服务器管理
sudo systemctl status nginx    # 检查服务状态
tail -f /var/log/application.log    # 监控日志
git pull && npm install    # 批量更新代码

# 开发环境
npm run dev    # 同时启动多个服务
docker-compose up    # 启动容器
pytest -v    # 运行测试
```

---

## 🔧 **插件管理**

### 📥 **安装新插件**
在 `wezterm.lua` 中添加：
```lua
local new_plugin = wezterm.plugin.require("https://github.com/author/plugin-name")
new_plugin.apply_to_config(config, {
   -- 插件配置选项
})
```

### 🔄 **更新插件**
运行以下命令更新所有插件：
```bash
# 在 WezTerm 中按 F12 打开调试控制台，然后输入：
wezterm.plugin.update_all()
```

### 📋 **列出已安装插件**
```bash
# 在调试控制台中运行：
wezterm.plugin.list()
```

### 🗑️ **删除插件**
1. 从配置文件中移除插件相关代码
2. 删除插件目录：
   - Windows: `%APPDATA%\wezterm\plugins\`
   - macOS: `~/Library/Application Support/wezterm/plugins/`
   - Linux: `~/.local/share/wezterm/plugins/`

---

## 🌟 **推荐插件**

### 🎨 **界面美化**
- **bar.wezterm**: 可配置的标签栏，包含电池状态等
- **tabline.wez**: 类似 lualine.nvim 的复古标签栏

### ⌨️ **键位扩展**
- **modal.wezterm**: Vim 风格的模式化键位绑定
- **wez-tmux**: 移植的 tmux 键位绑定

### 🗂️ **会话管理**
- **resurrect.wezterm**: 保存和恢复窗口、标签页状态
- **smart_workspace_switcher.wezterm**: 智能工作区切换器
- **sessionizer.wezterm**: 使用 fd 快速打开 Git 仓库

### 🔧 **实用工具**
- **toggle_terminal.wez**: 可切换的终端窗口
- **tunicodes**: 通过 Unicode 代码点插入字符

### 🔗 **Neovim 集成**
- **smart-splits.nvim**: 无缝窗格导航
- **wezterm-config.nvim**: 直接从 Neovim 修改 WezTerm 配置

---

## 💻 **开发自定义插件**

### 📝 **基本结构**
```lua
-- plugin/init.lua
local M = {}

function M.apply_to_config(config, opts)
   opts = opts or {}
   
   -- 添加键位绑定
   table.insert(config.keys, {
      key = opts.key or 'x',
      mods = opts.mods or 'ALT',
      action = wezterm.action_callback(function(window, pane)
         -- 插件功能实现
      end)
   })
end

return M
```

### 🔄 **开发流程**
1. 创建本地开发目录
2. 添加 `plugin/init.lua` 文件
3. 使用 `file://` 协议引用本地插件
4. 使用 `wezterm.plugin.update_all()` 同步更改

---

## 🚀 **cmd-sender 高级用法**

### 🎛️ **自定义配置**
```lua
cmd_sender.apply_to_config(config, {
   key = 'b',                           -- 快捷键
   mods = 'ALT|CTRL',                   -- 修饰键
   description = '向所有窗格发送命令'      -- 描述
})
```

### 💡 **工作流技巧**

#### 🌐 **多服务器运维**
```bash
# 1. 设置多个 SSH 连接窗格
Alt + 5     # SSH 连接器
# 创建到不同服务器的连接

# 2. 批量执行运维命令
Alt + Ctrl + B
> sudo systemctl restart nginx

# 3. 批量监控
Alt + Ctrl + B  
> htop
```

#### 🐳 **Docker 容器管理**
```bash
# 1. 分割窗格并连接不同容器
Alt + \     # 垂直分割
docker exec -it container1 bash
# 在另一个窗格
docker exec -it container2 bash

# 2. 批量操作
Alt + Ctrl + B
> ls -la /app
```

#### 🧪 **开发测试流程**
```bash
# 1. 创建多环境窗格
# 窗格1: 开发环境
# 窗格2: 测试环境  
# 窗格3: 生产环境

# 2. 批量部署
Alt + Ctrl + B
> git pull && npm install && npm run build
```

---

## 🐛 **故障排除**

### ❌ **插件加载失败**
1. 检查网络连接
2. 验证 GitHub URL 是否正确
3. 重启 WezTerm

### 🔄 **插件更新问题**
1. 手动删除插件目录
2. 重新启动 WezTerm 触发重新下载

### ⌨️ **快捷键冲突**
1. 检查 `config/bindings.lua` 中的键位绑定
2. 修改插件配置中的快捷键

### 🐞 **调试插件**
```bash
# 在调试控制台中检查错误
F12    # 打开调试控制台
# 查看错误信息和日志
```

---

## 📚 **参考资源**

### 🔗 **官方文档**
- [WezTerm 插件系统](https://wezterm.org/config/plugins.html)
- [Lua API 参考](https://wezterm.org/config/lua/index.html)

### 🌟 **插件集合**
- [Awesome WezTerm](https://github.com/michaelbrusegard/awesome-wezterm) - 精选插件列表

### 💬 **社区支持**
- [GitHub Discussions](https://github.com/wez/wezterm/discussions)
- [Matrix 聊天室](https://matrix.to/#/#wezterm:matrix.org)

---

## 🎉 **享受插件带来的便利！**

现在您的 WezTerm 不仅有美丽的界面和强大的功能，还有可扩展的插件系统！

### 🚀 **下一步**
- 探索更多插件
- 开发自定义插件
- 分享您的配置

> 💡 **提示**: 使用 `Alt + Ctrl + B` 开始体验批量命令发送功能！ 
-- 测试标签页颜色功能
local wezterm = require('wezterm')

-- 创建基础配置
local config = wezterm.config_builder()

-- 导入彩色标签页系统
require('events.tab-colors').setup()

-- 基础外观设置
config.font = wezterm.font('JetBrains Mono', { weight = 'Medium' })
config.font_size = 12
config.enable_tab_bar = true
config.tab_bar_at_bottom = true
config.use_fancy_tab_bar = false

-- 应用 Neapsix 主题
config.color_scheme = 'Neapsix (Rosé Pine)'

-- 设置背景色以便更好地看到标签页颜色
config.colors = {
   background = '#191724',
   foreground = '#e0def4',
}

-- 窗口设置
config.window_padding = {
   left = 0,
   right = 0,
   top = 5,
   bottom = 0,
}

-- 启动配置 - 创建多个不同类型的标签页进行测试
config.default_prog = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' }

-- 测试标签页启动配置
local launch_menu = {
   {
      label = '🔷 PowerShell 7',
      args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' },
   },
   {
      label = '🐙 Git Bash',
      args = { 'D:\\Git\\bin\\bash.exe', '-l' },
   },
   {
      label = '⚫ Command Prompt',
      args = { 'cmd.exe' },
   },
}

config.launch_menu = launch_menu

-- 简化的快捷键以便测试
config.keys = {
   -- F3 打开启动器来测试不同的标签页
   { key = 'F3', mods = 'NONE', action = wezterm.action.ShowLauncher },
   
   -- 快速启动不同类型的终端进行测试
   { 
      key = '1', 
      mods = 'ALT', 
      action = wezterm.action.SpawnCommandInNewTab({
         label = 'PowerShell 7',
         args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' }
      })
   },
   { 
      key = '2', 
      mods = 'ALT', 
      action = wezterm.action.SpawnCommandInNewTab({
         label = 'Git Bash',
         args = { 'D:\\Git\\bin\\bash.exe', '-l' }
      })
   },
   { 
      key = '3', 
      mods = 'ALT', 
      action = wezterm.action.SpawnCommandInNewTab({
         label = 'Command Prompt',
         args = { 'cmd.exe' }
      })
   },
}

-- 禁用默认快捷键
config.disable_default_key_bindings = false

print('🌸 标签页颜色测试配置已加载')
print('🔥 使用说明:')
print('   Alt+1: 打开 PowerShell 7 (蓝色)')
print('   Alt+2: 打开 Git Bash (绿色)')
print('   Alt+3: 打开 Command Prompt (黄色)')
print('   F3: 打开启动器查看更多选项')
print('💡 观察不同类型的标签页显示不同的颜色和图标!')

return config 
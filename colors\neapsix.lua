-- neapsix / Ros<PERSON> for WezTerm
-- Based on the official neapsix Rosé Pine color scheme
-- "All natural pine, faux fur and a bit of soho vibes for the classy minimalist"

-- Rosé Pine color palette
local palette = {
   -- Base colors
   base = '#191724',
   surface = '#1f1d2e',
   overlay = '#26233a',
   muted = '#6e6a86',
   subtle = '#908caa',
   text = '#e0def4',
   love = '#eb6f92',
   gold = '#f6c177',
   rose = '#ebbcba',
   pine = '#31748f',
   foam = '#9ccfd8',
   iris = '#c4a7e7',

   -- Special colors
   highlight_low = '#21202e',
   highlight_med = '#403d52',
   highlight_high = '#524f67',
}

local colors = {
   -- Basic terminal colors
   foreground = palette.text,
   background = palette.base,
   
   -- Cursor colors
   cursor_bg = palette.rose,
   cursor_border = palette.rose,
   cursor_fg = palette.base,
   
   -- Selection colors
   selection_bg = palette.highlight_med,
   selection_fg = palette.text,
   
   -- Scrollbar
   scrollbar_thumb = palette.overlay,
   
   -- Split line color
   split = palette.highlight_med,
   
   -- ANSI color palette
   ansi = {
      palette.overlay,    -- black
      palette.love,       -- red
      palette.pine,       -- green  
      palette.gold,       -- yellow
      palette.foam,       -- blue
      palette.iris,       -- magenta
      palette.rose,       -- cyan
      palette.text,       -- white
   },
   
   -- Bright ANSI colors
   brights = {
      palette.muted,      -- bright black
      palette.love,       -- bright red
      palette.pine,       -- bright green
      palette.gold,       -- bright yellow
      palette.foam,       -- bright blue
      palette.iris,       -- bright magenta
      palette.rose,       -- bright cyan
      palette.text,       -- bright white
   },
   
   -- Tab bar styling
   tab_bar = {
      background = 'rgba(25, 23, 36, 0.95)', -- base with transparency
      
      active_tab = {
         bg_color = palette.surface,
         fg_color = palette.text,
         intensity = 'Normal',
         underline = 'None',
         italic = false,
         strikethrough = false,
      },
      
      inactive_tab = {
         bg_color = palette.base,
         fg_color = palette.muted,
      },
      
      inactive_tab_hover = {
         bg_color = palette.highlight_low,
         fg_color = palette.subtle,
         italic = true,
      },
      
      new_tab = {
         bg_color = palette.base,
         fg_color = palette.muted,
      },
      
      new_tab_hover = {
         bg_color = palette.highlight_low,
         fg_color = palette.text,
         italic = true,
      },
      
      -- Tab bar edge color
      inactive_tab_edge = palette.highlight_med,
   },
   
   -- Visual bell
   visual_bell = palette.love,
   
   -- Additional indexed colors
   indexed = {
      [16] = palette.gold,
      [17] = palette.rose,
   },
   
   -- Compose cursor (for IME)
   compose_cursor = palette.foam,
   
   -- Copy mode colors
   copy_mode_active_highlight_bg = { Color = palette.highlight_high },
   copy_mode_active_highlight_fg = { Color = palette.text },
   copy_mode_inactive_highlight_bg = { Color = palette.highlight_med },
   copy_mode_inactive_highlight_fg = { Color = palette.subtle },
   
   -- Quick select colors
   quick_select_label_bg = { Color = palette.love },
   quick_select_label_fg = { Color = palette.base },
   quick_select_match_bg = { Color = palette.pine },
   quick_select_match_fg = { Color = palette.text },
}

-- Window frame colors for fancy tab bar
local window_frame = {
   active_titlebar_bg = palette.base,
   inactive_titlebar_bg = palette.base,
}

return {
   colors = colors,
   window_frame = window_frame,
   -- This theme looks best with background opacity
   window_background_opacity = 0.95,
} 
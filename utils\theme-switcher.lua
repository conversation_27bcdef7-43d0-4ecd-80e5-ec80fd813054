local wezterm = require('wezterm')

local ThemeSwitcher = {}

-- 可用的主题列表
ThemeSwitcher.themes = {
   {
      name = 'neapsix',
      display_name = '🌸 Neapsix (Rosé Pine)',
      module = 'colors.neapsix',
      description = 'All natural pine, faux fur and a bit of soho vibes'
   },
   {
      name = 'custom',
      display_name = '🎨 Custom (Catppuccin Mocha)',
      module = 'colors.custom',
      description = 'Slightly altered Catppuccin Mocha theme'
   }
}

-- 获取当前主题名称
function ThemeSwitcher:get_current_theme()
   local config_file = wezterm.config_dir .. '/current_theme.txt'
   local f = io.open(config_file, 'r')
   if f then
      local theme = f:read('*line')
      f:close()
      return theme or 'neapsix'
   end
   return 'neapsix' -- 默认主题
end

-- 保存当前主题
function ThemeSwitcher:save_current_theme(theme_name)
   local config_file = wezterm.config_dir .. '/current_theme.txt'
   local f = io.open(config_file, 'w')
   if f then
      f:write(theme_name)
      f:close()
   end
end

-- 获取主题配置
function ThemeSwitcher:get_theme_config(theme_name)
   for _, theme in ipairs(self.themes) do
      if theme.name == theme_name then
         local theme_module = require(theme.module)
         return {
            colors = theme_module.colors,
            window_frame = theme_module.window_frame,
            window_background_opacity = theme_module.window_background_opacity or 1.0,
         }
      end
   end
   -- 如果找不到主题，返回默认的 neapsix
   local neapsix = require('colors.neapsix')
   return {
      colors = neapsix.colors,
      window_frame = neapsix.window_frame,
      window_background_opacity = neapsix.window_background_opacity or 0.95,
   }
end

-- 创建主题选择器动作
function ThemeSwitcher:create_theme_selector()
   local choices = {}
   
   for _, theme in ipairs(self.themes) do
      table.insert(choices, {
         id = theme.name,
         label = theme.display_name .. ' - ' .. theme.description
      })
   end
   
   return wezterm.action.InputSelector({
      title = '🎨 选择主题 / Choose Theme',
      choices = choices,
      fuzzy = true,
      fuzzy_description = '搜索主题: ',
      action = wezterm.action_callback(function(window, pane, id, label)
         if id then
            -- 保存选择的主题
            ThemeSwitcher:save_current_theme(id)
            
            -- 重新加载配置
            wezterm.reload_configuration()
            
            -- 显示通知
            local selected_theme = nil
            for _, theme in ipairs(ThemeSwitcher.themes) do
               if theme.name == id then
                  selected_theme = theme
                  break
               end
            end
            
            if selected_theme then
               window:toast_notification('WezTerm 主题', '已切换到: ' .. selected_theme.display_name, nil, 3000)
            end
         end
      end)
   })
end

-- 快速切换到下一个主题
function ThemeSwitcher:cycle_themes()
   local current = self:get_current_theme()
   local current_index = 1
   
   -- 找到当前主题的索引
   for i, theme in ipairs(self.themes) do
      if theme.name == current then
         current_index = i
         break
      end
   end
   
   -- 切换到下一个主题
   local next_index = (current_index % #self.themes) + 1
   local next_theme = self.themes[next_index]
   
   return wezterm.action_callback(function(window, pane)
      self:save_current_theme(next_theme.name)
      wezterm.reload_configuration()
      window:toast_notification('WezTerm 主题', '已切换到: ' .. next_theme.display_name, nil, 3000)
   end)
end

-- 应用主题到配置
function ThemeSwitcher:apply_to_config(config)
   local current_theme = self:get_current_theme()
   local theme_config = self:get_theme_config(current_theme)
   
   config.colors = theme_config.colors
   config.window_frame = theme_config.window_frame
   config.window_background_opacity = theme_config.window_background_opacity
   
   return config
end

return ThemeSwitcher 
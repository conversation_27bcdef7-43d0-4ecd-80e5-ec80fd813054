-- 简单的WezTerm配置测试脚本
local wezterm = require('wezterm')

-- 测试基本配置加载
print("测试WezTerm配置...")

-- 测试平台检测
local platform = require('utils.platform')
print("平台检测:", platform.is_win and "Windows" or "其他")

-- 测试SSH管理器
local ssh_connections = require('config.ssh-connections')
print("SSH连接数量:", #ssh_connections:get_domains())

-- 测试启动配置
local launch_config = require('config.launch')
print("启动菜单项目数量:", #launch_config.launch_menu)

-- 测试域配置
local domains_config = require('config.domains')
print("SSH域数量:", #domains_config.ssh_domains)
print("WSL域数量:", #domains_config.wsl_domains)

print("配置测试完成!")

return {
    -- 最基本的配置
    default_prog = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' },
} 
# 简化版 Resurrect 修复脚本
Write-Host "修复 Resurrect 插件状态保存问题" -ForegroundColor Cyan

$resurrectDir = "$env:LOCALAPPDATA\wezterm\resurrect_states"
$subDirs = @("workspace", "window", "tab")

# 删除现有目录
if (Test-Path $resurrectDir) {
    Remove-Item $resurrectDir -Recurse -Force
    Write-Host "已删除现有目录" -ForegroundColor Yellow
}

# 创建目录结构
foreach ($subDir in $subDirs) {
    $fullPath = Join-Path $resurrectDir $subDir
    New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
    Write-Host "创建目录: $fullPath" -ForegroundColor Green
}

# 测试写入权限
foreach ($subDir in $subDirs) {
    $testFile = Join-Path $resurrectDir $subDir "test.json"
    try {
        '{"test": "data"}' | Out-File -FilePath $testFile -Force -Encoding UTF8
        if (Test-Path $testFile) {
            Write-Host "$subDir 目录写入测试: 成功" -ForegroundColor Green
            Remove-Item $testFile -Force
        }
    } catch {
        Write-Host "$subDir 目录写入测试: 失败" -ForegroundColor Red
    }
}

# 创建默认工作区文件
$defaultFile = Join-Path $resurrectDir "workspace" "default.json"
$defaultContent = '{"format_version": "1.0", "workspace_name": "default", "tabs": []}'
try {
    $defaultContent | Out-File -FilePath $defaultFile -Force -Encoding UTF8
    Write-Host "创建默认工作区配置: 成功" -ForegroundColor Green
} catch {
    Write-Host "创建默认工作区配置: 失败" -ForegroundColor Red
}

Write-Host "修复完成! 请重启 WezTerm 测试" -ForegroundColor Green 
-- 🔌 WezTerm 推荐插件综合测试配置
local wezterm = require('wezterm')

-- 创建基础配置
local config = wezterm.config_builder()

-- 🚀 加载所有推荐插件
wezterm.log_info('🔌 正在加载推荐插件...')

-- 1. cmd-sender 插件 - 批量命令发送
local cmd_sender = wezterm.plugin.require("https://github.com/aureolebigben/wezterm-cmd-sender")
wezterm.log_info('✅ cmd-sender 插件已加载')

-- 2. resurrect 插件 - 会话管理
local resurrect = wezterm.plugin.require("https://github.com/MLFlexer/resurrect.wezterm")
wezterm.log_info('✅ resurrect 插件已加载')

-- 3. 智能工作区切换器
local workspace_switcher = wezterm.plugin.require("https://github.com/MLFlexer/smart_workspace_switcher.wezterm")
wezterm.log_info('✅ smart_workspace_switcher 插件已加载')

-- 4. sessionizer 插件 - 项目切换 (暂时注释)
-- local sessionizer = wezterm.plugin.require("https://github.com/ElCapitanSponge/sessionizer.wezterm")
-- wezterm.log_info('✅ sessionizer 插件已加载')

-- 🎨 基础外观配置
config.font = wezterm.font('JetBrains Mono', { weight = 'Medium' })
config.font_size = 12
config.enable_tab_bar = true
config.tab_bar_at_bottom = true
config.use_fancy_tab_bar = false

-- Neapsix 主题配置
config.colors = {
   background = '#191724',
   foreground = '#e0def4',
   cursor_bg = '#f6c177',
   cursor_fg = '#191724',
   selection_bg = '#403d52',
   selection_fg = '#e0def4',
}

-- 窗口设置
config.window_padding = {
   left = 10,
   right = 10,
   top = 5,
   bottom = 0,
}

-- 🔧 插件配置

-- sessionizer 项目目录配置 (Windows路径) - 暂时注释
-- local username = os.getenv("USERNAME") or "User"
-- local projects = {
--    wezterm.home_dir .. "\\Documents",
--    wezterm.home_dir .. "\\Desktop", 
--    "D:\\Projects",
--    "D:\\Git",
--    "C:\\Users\\<USER>\\source\\repos"
-- }
-- sessionizer.set_projects(projects)

-- 应用插件到配置
cmd_sender.apply_to_config(config, {
   key = 'b',
   mods = 'ALT|CTRL',
   description = '向所有窗格发送命令'
})

-- sessionizer.configure(config) -- 暂时注释
workspace_switcher.apply_to_config(config)

-- 📁 Windows 状态目录设置
local username = os.getenv("USERNAME") or "User"
resurrect.state_manager.change_state_save_dir("C:\\Users\\<USER>\\AppData\\Local\\wezterm\\resurrect_states\\")

-- 🕒 自动保存配置 (测试时设为5分钟)
resurrect.state_manager.periodic_save({
   interval_seconds = 5 * 60,  -- 5分钟
   save_workspaces = true,
   save_windows = true,
   save_tabs = true,
})

-- 🔄 事件监听器
wezterm.on("smart_workspace_switcher.workspace_switcher.chosen", function(window, workspace)
   local gui_win = window:gui_window()
   local base_path = string.gsub(workspace, "(.*[/\\])(.*)", "%2")
   gui_win:set_right_status(wezterm.format({
      { Foreground = { Color = "#c4a7e7" } },
      { Text = "📁 " .. base_path .. "  " },
   }))
end)

-- 📚 测试快捷键
config.keys = config.keys or {}

-- 插件测试快捷键
local test_keys = {
   -- resurrect 测试
   {
      key = 'r',
      mods = 'ALT',
      action = wezterm.action_callback(function(win, pane)
         resurrect.fuzzy_loader.fuzzy_load(win, pane, function(id, label)
            local type = string.match(id, "^([^/]+)")
            id = string.match(id, "([^/]+)$")
            id = string.match(id, "(.+)%..+$")
            local opts = {
               relative = true,
               restore_text = true,
               on_pane_restore = resurrect.tab_state.default_on_pane_restore,
            }
            if type == "workspace" then
               local state = resurrect.state_manager.load_state(id, "workspace")
               resurrect.workspace_state.restore_workspace(state, opts)
            elseif type == "window" then
               local state = resurrect.state_manager.load_state(id, "window")
               resurrect.window_state.restore_window(pane:window(), state, opts)
            elseif type == "tab" then
               local state = resurrect.state_manager.load_state(id, "tab")
               resurrect.tab_state.restore_tab(pane:tab(), state, opts)
            end
         end)
      end),
   },
   
   -- 保存状态测试
   {
      key = 's',
      mods = 'ALT',
      action = wezterm.action_callback(function(win, pane)
         resurrect.state_manager.save_state(resurrect.workspace_state.get_workspace_state())
         win:toast_notification('WezTerm', '💾 状态已保存', nil, 2000)
      end),
   },

   -- 快速分割窗格进行测试
   {
      key = '1',
      mods = 'ALT',
      action = wezterm.action.SplitVertical({ domain = 'CurrentPaneDomain' })
   },
   
   {
      key = '2',
      mods = 'ALT',
      action = wezterm.action.SplitHorizontal({ domain = 'CurrentPaneDomain' })
   },

   -- 帮助信息
   {
      key = 'h',
      mods = 'ALT',
      action = wezterm.action_callback(function(window, pane)
         window:toast_notification(
            '🔌 插件测试模式',
            '🚀 推荐插件功能测试:\n\n' ..
            '💾 Alt+S: 保存当前工作区状态\n' ..
            '🔄 Alt+R: 模糊搜索并恢复状态\n' ..
            '📦 Alt+Ctrl+B: 向所有窗格发送命令\n' ..
            '🏢 Alt+A: 工作区切换器 (workspace_switcher)\n' ..
            -- '📁 Leader+F: 项目切换器 (sessionizer)\n' .. -- 暂时注释
            '\n🔧 窗格操作:\n' ..
            'Alt+1: 垂直分割窗格\n' ..
            'Alt+2: 水平分割窗格\n' ..
            'Alt+H: 显示此帮助\n\n' ..
            '🎯 测试流程:\n' ..
            '1. 创建多个窗格和标签页\n' ..
            '2. 保存状态 (Alt+S)\n' ..
            '3. 关闭终端重新打开\n' ..
            '4. 恢复状态 (Alt+R)\n' ..
            '5. 测试批量命令 (Alt+Ctrl+B)',
            nil, 10000
         )
      end)
   }
}

-- 合并测试快捷键
for _, key in ipairs(test_keys) do
   table.insert(config.keys, key)
end

-- 设置启动程序
config.default_prog = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' }

-- Leader 键配置
config.leader = {
   key = 'a',
   mods = 'ALT|CTRL',
   timeout_milliseconds = 1000
}

-- 🚀 启动时恢复会话
wezterm.on("gui-startup", resurrect.state_manager.resurrect_on_gui_startup)

wezterm.log_info('🎉 插件测试配置加载完成!')
wezterm.log_info('📋 测试说明:')
wezterm.log_info('  💾 Alt+S: 保存当前状态')
wezterm.log_info('  🔄 Alt+R: 恢复状态选择器')
wezterm.log_info('  📦 Alt+Ctrl+B: 批量命令发送')
wezterm.log_info('  🏢 Alt+A: 工作区切换器')
-- wezterm.log_info('  📁 Leader+F: 项目切换器') -- 暂时注释
wezterm.log_info('  🔧 Alt+1/2: 创建窗格进行测试')
wezterm.log_info('  ❓ Alt+H: 显示详细帮助')
wezterm.log_info('')
wezterm.log_info('🌟 自动功能:')
wezterm.log_info('  ⏰ 每5分钟自动保存状态')
wezterm.log_info('  🔄 工作区切换时自动保存/恢复')
wezterm.log_info('  📊 状态栏显示当前工作区路径')

return config 
# 🔌 WezTerm 推荐插件完整配置指南

您的 WezTerm 配置现在集成了4个精选的实用插件，大幅提升终端使用体验！

---

## 📦 **已配置的推荐插件**

### 1. 🔄 **resurrect.wezterm** - 会话管理神器 ⭐⭐⭐⭐⭐

**功能**: 保存和恢复终端会话状态  
**作者**: MLFlexer  
**快捷键**: `Leader + R` (恢复), `Leader + S` (保存)

#### ✨ **强大功能**
- **完整状态保存**: 窗口、标签页、窗格布局、工作目录
- **程序状态恢复**: 支持 vim/neovim 会话恢复
- **自动保存**: 每15分钟自动保存工作区状态
- **启动恢复**: 启动时自动恢复上次会话
- **加密存储**: 可选加密保存敏感信息

#### 🚀 **使用场景**
```bash
# 日常工作流
1. 打开多个项目窗格和标签页
2. 配置完美的开发环境布局
3. 自动保存状态或手动保存 (Leader + S)
4. 关闭终端
5. 重新打开时自动恢复或手动选择恢复 (Leader + R)
```

#### 📁 **Windows 存储位置**
```
C:\Users\<USER>\AppData\Local\wezterm\resurrect_states\
```

---

### 2. 🏢 **smart_workspace_switcher.wezterm** - 智能工作区切换 ⭐⭐⭐⭐

**功能**: 基于 zoxide 的模糊工作区切换器  
**作者**: MLFlexer  
**快捷键**: `Alt + A` (工作区切换器)

#### ✨ **强大功能**
- **模糊搜索**: 基于 zoxide 历史记录
- **智能匹配**: 自动学习常用目录
- **快速创建**: 不存在的工作区自动创建
- **状态显示**: 右侧状态栏显示当前工作区路径

#### 🚀 **使用方法**
1. 按 `Alt + A` 打开工作区选择器
2. 输入项目名称或路径的一部分
3. 回车确认，自动切换或创建工作区
4. 状态栏显示当前工作区名称

#### 🔗 **与 resurrect 的完美集成**
- 切换工作区时自动保存当前状态
- 切换到新工作区时自动恢复状态
- 无缝的项目切换体验

---

### 3. 📁 **sessionizer.wezterm** - 项目快速启动器 ⭐⭐⭐

**功能**: 类似 tmux-sessionizer 的项目快速切换  
**作者**: ElCapitanSponge  
**快捷键**: `Leader + F` (项目选择器)

#### ✨ **强大功能**
- **项目扫描**: 自动扫描配置的项目目录
- **Git 仓库识别**: 智能识别 Git 项目
- **快速启动**: 一键切换到项目工作区
- **模糊搜索**: 支持项目名称模糊匹配

#### 📂 **已配置的项目目录**
```lua
~/Documents          -- 文档目录
~/Desktop           -- 桌面目录  
D:/Projects         -- 项目目录
D:/Git             -- Git 仓库目录
C:/Users/<USER>/source/repos  -- Visual Studio 项目
```

#### 🔧 **自定义项目目录**
在 `wezterm.lua` 中修改 `projects` 数组：
```lua
local projects = {
    "C:/MyProjects",
    "D:/Workspace", 
    wezterm.home_dir .. "/development"
}
```

---

### 4. 📦 **cmd-sender.wezterm** - 批量命令发送 ⭐⭐⭐

**功能**: 向所有窗格同时发送命令  
**作者**: aureolebigben  
**快捷键**: `Alt + Ctrl + B`

#### ✨ **强大功能**
- **批量执行**: 同时向所有窗格发送命令
- **输入提示**: 友好的命令输入界面
- **实时执行**: 命令立即在所有窗格执行

#### 🚀 **实用场景**
```bash
# 服务器运维
sudo systemctl status nginx
htop
tail -f /var/log/app.log

# 开发环境
npm install
git pull
docker-compose up

# 监控命令
watch df -h
ping google.com
```

---

## 🎮 **完整快捷键参考**

### 🔌 **插件快捷键**
| 快捷键 | 功能 | 插件 |
|--------|------|------|
| `Leader + R` | **恢复会话选择器** | resurrect |
| `Leader + S` | **保存当前会话** | resurrect |
| `Alt + A` | **工作区切换器** | workspace_switcher |
| `Leader + F` | **项目选择器** | sessionizer |
| `Alt + Ctrl + B` | **批量命令发送** | cmd-sender |

### 🧪 **测试模式快捷键**
| 快捷键 | 功能 |
|--------|------|
| `Alt + H` | **显示插件帮助** |
| `Alt + S` | **快速保存状态** |
| `Alt + R` | **快速恢复状态** |
| `Alt + 1` | **垂直分割窗格** |
| `Alt + 2` | **水平分割窗格** |

---

## 🚀 **高效工作流示例**

### 🏗️ **项目开发流程**
```bash
# 1. 快速切换到项目
Leader + F → 选择项目 → 回车

# 2. 设置开发环境
Alt + 1    # 垂直分割窗格
Alt + 2    # 水平分割窗格

# 3. 在不同窗格中设置环境
# 窗格1: 编辑器 (vim/code)
# 窗格2: 开发服务器 (npm run dev)
# 窗格3: 日志监控 (tail -f logs)

# 4. 保存完美的布局
Leader + S  # 保存会话状态

# 5. 下次直接恢复
Leader + R → 选择会话 → 回车
```

### 🌐 **多服务器运维**
```bash
# 1. 创建多个SSH连接窗格
Alt + 5    # SSH连接器
# 连接到不同服务器

# 2. 批量执行运维命令
Alt + Ctrl + B
> sudo systemctl restart nginx

# 3. 批量监控
Alt + Ctrl + B  
> htop

# 4. 保存运维会话
Leader + S
```

### 📊 **数据分析工作流**
```bash
# 1. 切换到数据项目
Leader + F → "data-analysis" → 回车

# 2. 恢复之前的分析环境
Leader + R → 选择数据分析会话

# 3. 批量启动Jupyter
Alt + Ctrl + B
> jupyter lab
```

---

## 🔧 **高级配置**

### 📁 **自定义状态保存位置**
```lua
-- 在 wezterm.lua 中修改
resurrect.state_manager.change_state_save_dir("D:/wezterm-sessions/")
```

### ⏰ **调整自动保存间隔**
```lua
-- 修改自动保存间隔为30分钟
resurrect.state_manager.periodic_save({
   interval_seconds = 30 * 60,
   save_workspaces = true,
   save_windows = true,
   save_tabs = true,
})
```

### 🔐 **启用状态加密** (可选)
```lua
-- 需要安装 age 加密工具
resurrect.state_manager.set_encryption({
   enable = true,
   method = "age",
   private_key = "C:/path/to/private/key.txt",
   public_key = "age1ql3z7hjy54pw3hyww5ayyfg7zqgvc7w3j2elw8zmrj2kg5sfn9aqmcac8p",
})
```

### 🎨 **自定义工作区状态栏**
```lua
wezterm.on("smart_workspace_switcher.workspace_switcher.chosen", function(window, workspace)
   local gui_win = window:gui_window()
   local base_path = string.gsub(workspace, "(.*[/\\])(.*)", "%2")
   gui_win:set_right_status(wezterm.format({
      { Foreground = { Color = "#your_color" } },
      { Text = "🏢 " .. base_path .. "  " },
   }))
end)
```

---

## 🧪 **插件测试指南**

### 🚀 **快速测试**
```bash
# 1. 运行测试配置
wezterm --config-file test-plugins.lua

# 2. 按 Alt+H 查看测试帮助

# 3. 测试流程：
#   - Alt+1/2: 创建窗格布局
#   - Alt+S: 保存状态
#   - 关闭终端重新打开
#   - Alt+R: 恢复状态
#   - Alt+Ctrl+B: 测试批量命令
```

### 📊 **功能验证清单**
- [ ] resurrect 保存/恢复功能正常
- [ ] workspace_switcher 工作区切换正常
- [ ] sessionizer 项目切换正常
- [ ] cmd-sender 批量命令发送正常
- [ ] 自动保存功能启用
- [ ] 状态栏显示工作区路径
- [ ] 所有快捷键响应正常

---

## 🐛 **故障排除**

### ❌ **常见问题**

#### 1. **插件加载失败**
```bash
# 检查网络连接，手动下载插件
# 插件存储位置: C:\Users\<USER>\AppData\Roaming\wezterm\plugins\
```

#### 2. **resurrect 状态保存失败**
```bash
# 检查目录权限
# 手动创建状态目录: 
mkdir "C:\Users\<USER>\AppData\Local\wezterm\resurrect_states"
```

#### 3. **workspace_switcher 需要 zoxide**
```bash
# Windows 安装 zoxide:
winget install ajeetdsouza.zoxide
# 或
scoop install zoxide
```

#### 4. **sessionizer 找不到项目**
```bash
# 检查项目目录是否存在
# 修改 wezterm.lua 中的 projects 配置
```

### 🔍 **调试模式**
```bash
# 启用调试输出
wezterm --config-file test-plugins.lua --debug

# 查看插件日志
F12  # 打开调试控制台
```

---

## 🌟 **插件生态推荐**

### 🎨 **界面美化插件** (未配置，可选)
- **bar.wezterm**: 功能丰富的标签栏
- **tabline.wez**: 类似 lualine.nvim 的标签栏

### ⌨️ **键位增强插件** (未配置，可选)
- **modal.wezterm**: Vim 风格模式化键位
- **wez-tmux**: tmux 键位绑定

### 🔧 **实用工具插件** (未配置，可选)
- **toggle_terminal.wez**: 可切换终端窗口
- **tunicodes**: Unicode 字符插入

---

## 🎉 **享受增强后的 WezTerm！**

现在您的 WezTerm 拥有了：

### ✅ **已实现的增强功能**
- 🔄 **智能会话管理**: 永不丢失工作状态
- 🏢 **快速工作区切换**: 项目间无缝切换  
- 📁 **项目快速启动**: 一键进入开发环境
- 📦 **批量命令执行**: 高效运维操作
- ⏰ **自动状态保存**: 无需手动操作
- 📊 **智能状态显示**: 实时显示工作区信息

### 🚀 **下一步建议**
1. **熟悉快捷键**: 多练习使用各种插件功能
2. **自定义配置**: 根据个人需求调整插件设置
3. **探索更多插件**: 浏览 [awesome-wezterm](https://github.com/michaelbrusegard/awesome-wezterm)
4. **分享配置**: 与团队分享高效的配置

> 💡 **提示**: 这4个插件的组合为您提供了企业级的终端会话管理能力，大幅提升开发效率！ 
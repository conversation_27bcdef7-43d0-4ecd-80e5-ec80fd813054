# WezTerm SSH 配置错误修复指南

## 🚨 错误信息

```
Error converting lua value returned by script C:\Users\<USER>\.config\wezterm\wezterm.lua to Config struct: error converting Lua table to config::config::Config (Error processing ssh_domains.ssh_option (types: Config, SshDomain) Cannot convert `I64` to `String`)
```

## 🔍 错误原因

这个错误是因为在SSH配置中，`Port` 字段被设置为数字类型（I64），但WezTerm期望它是字符串类型。

## ✅ 解决方案

### 1. 已修复的文件

我已经修复了以下文件中的类型错误：

#### `config/ssh-connections.lua`
- 将 `Port = 22` 改为 `Port = "22"`
- 暂时注释了WSL SSH连接以避免配置错误

#### `utils/ssh-manager.lua`
- 在 `add_quick_connection` 方法中添加了 `tostring(port)` 转换

### 2. 修复步骤

1. **重启 WezTerm**：应用新的配置更改
2. **测试基本功能**：按 `F3` 查看启动器是否正常工作
3. **如果仍有问题**：使用以下临时配置

## 🛠️ 临时配置（如果仍有问题）

如果错误仍然存在，请暂时使用简化的domains配置：

```lua
-- 编辑 config/domains.lua
return {
   ssh_domains = {},  -- 暂时为空
   unix_domains = {},
   wsl_domains = {
      {
         name = 'WSL:Kali',
         distribution = 'kali-linux',
         username = 'kali',
         default_cwd = '/home/<USER>',
         default_prog = { 'bash', '-l' },
      },
   },
}
```

## 🔧 SSH配置最佳实践

### 正确的端口配置
```lua
-- ✅ 正确 - 端口使用字符串
ssh_option = {
    Port = "22",
    IdentityFile = "path/to/key"
}

-- ❌ 错误 - 端口使用数字
ssh_option = {
    Port = 22,  -- 这会导致类型错误
    IdentityFile = "path/to/key"
}
```

### 添加SSH连接的正确方法
```lua
-- 方法1: 使用快速连接（端口会自动转换为字符串）
ssh_manager:add_quick_connection(
    'my-server',
    'example.com',
    'username',
    22,  -- 这里可以使用数字，会自动转换
    nil
)

-- 方法2: 使用完整配置（端口必须是字符串）
ssh_manager:add_connection({
    name = 'my-server',
    remote_address = 'example.com',
    username = 'username',
    ssh_option = {
        Port = "22",  -- 必须是字符串
        IdentityFile = "path/to/key"
    },
    multiplexing = 'None',
    default_prog = { 'bash', '-l' },
    assume_shell = 'Posix',
})
```

## 🧪 测试配置

运行测试脚本检查配置：
```bash
wezterm cli validate-config
```

或者使用项目中的测试文件：
```bash
wezterm --config-file test-config.lua
```

## 📝 常见类型错误

WezTerm 对配置类型要求严格，以下是常见的类型错误：

| 字段 | 错误类型 | 正确类型 | 示例 |
|------|----------|----------|------|
| Port | number | string | `"22"` |
| timeout_milliseconds | number | number | `5000` |
| multiplexing | string | string | `"None"` |

## 🎯 下一步

1. **重启 WezTerm** 应用修复
2. **按 F3** 测试启动器功能
3. **按 Alt+S** 测试SSH功能（如果已配置）
4. **如果需要SSH到WSL**，请先在WSL中配置SSH服务

## 🔗 参考资源

- [WezTerm SSH域配置](https://wezfurlong.org/wezterm/config/lua/SshDomain.html)
- [WezTerm WSL域配置](https://wezfurlong.org/wezterm/config/lua/WslDomain.html)
- [WezTerm 配置验证](https://wezfurlong.org/wezterm/cli/index.html) 
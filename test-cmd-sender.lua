-- 测试 cmd-sender 插件功能
local wezterm = require('wezterm')

-- 创建基础配置
local config = wezterm.config_builder()

-- 加载 cmd-sender 插件
local cmd_sender = wezterm.plugin.require("https://github.com/aureolebigben/wezterm-cmd-sender")

-- 基础外观设置
config.font = wezterm.font('JetBrains Mono', { weight = 'Medium' })
config.font_size = 12
config.enable_tab_bar = true
config.tab_bar_at_bottom = true
config.use_fancy_tab_bar = false

-- 应用 Neapsix 主题
config.colors = {
   background = '#191724',
   foreground = '#e0def4',
}

-- 窗口设置
config.window_padding = {
   left = 5,
   right = 5,
   top = 5,
   bottom = 5,
}

-- 应用 cmd-sender 插件配置
cmd_sender.apply_to_config(config, {
   key = 'b',
   mods = 'ALT|CTRL',
   description = '向所有窗格发送命令 - 测试模式'
})

-- 添加一些测试用的快捷键
config.keys = config.keys or {}

-- 快速创建窗格进行测试
table.insert(config.keys, {
   key = '1',
   mods = 'ALT',
   action = wezterm.action.SplitVertical({ domain = 'CurrentPaneDomain' })
})

table.insert(config.keys, {
   key = '2', 
   mods = 'ALT',
   action = wezterm.action.SplitHorizontal({ domain = 'CurrentPaneDomain' })
})

-- 显示快捷键帮助
table.insert(config.keys, {
   key = 'h',
   mods = 'ALT',
   action = wezterm.action_callback(function(window, pane)
      window:toast_notification(
         'cmd-sender 测试模式',
         '🚀 快捷键:\n' ..
         'Alt+1: 垂直分割窗格\n' ..
         'Alt+2: 水平分割窗格\n' ..
         'Alt+Ctrl+B: 向所有窗格发送命令\n' ..
         'Alt+H: 显示此帮助',
         nil, 5000
      )
   end)
})

-- 启动配置
config.default_prog = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' }

print('🔌 cmd-sender 插件测试配置已加载')
print('📋 测试流程:')
print('  1. Alt+1 或 Alt+2: 创建多个窗格')
print('  2. Alt+Ctrl+B: 向所有窗格发送命令')
print('  3. Alt+H: 显示帮助信息')
print('💡 测试命令示例: echo "Hello from all panes!" 或 pwd')

return config 
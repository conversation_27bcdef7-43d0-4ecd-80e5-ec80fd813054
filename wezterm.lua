local Config = require('config')
local wezterm = require('wezterm')

-- 🔌 插件声明 (延迟加载以避免重复)
local cmd_sender, resurrect, workspace_switcher, sessionizer

-- 🚩 控制插件和事件只加载一次的标志
local plugins_have_been_loaded = false
local events_have_been_registered = false

-- sessionizer 项目目录配置 (将在插件加载时设置)

require('utils.backdrops')
   -- :set_focus('#000000')
   -- :set_images_dir(require('wezterm').home_dir .. '/Pictures/Wallpapers/')
   :set_images()
   :random()

require('events.left-status').setup()
require('events.right-status').setup({ date_format = '%a %H:%M:%S' })
-- require('events.tab-title').setup({ hide_active_tab_unseen = false, unseen_icon = 'circle' })  -- 已被 tab-colors 替代
require('events.new-tab-button').setup()
require('events.tab-colors').setup()

local config = Config:init()
   :append(require('config.appearance'))
   :append(require('config.bindings'))
   :append(require('config.domains'))
   :append(require('config.fonts'))
   :append(require('config.launch'))

-- 修改 config.general 或直接设置
if config.options.general then
    config.options.general.automatically_reload_config = false
else
    config.options.automatically_reload_config = false
end

-- 🔧 插件配置 (添加重复加载保护)
if not plugins_have_been_loaded then
   wezterm.log_info('🔌 正在加载推荐插件...')
   
   -- 1. cmd-sender 插件 - 向所有窗格发送命令
   cmd_sender = wezterm.plugin.require("https://github.com/aureolebigben/wezterm-cmd-sender")
   
   -- 2. resurrect 插件 - 会话管理 ⭐⭐⭐⭐⭐
   resurrect = wezterm.plugin.require("https://github.com/MLFlexer/resurrect.wezterm")
   
   -- 3. 智能工作区切换器 ⭐⭐⭐⭐
   workspace_switcher = wezterm.plugin.require("https://github.com/MLFlexer/smart_workspace_switcher.wezterm")
   
   -- 4. sessionizer 插件 - 项目快速切换 ⭐⭐⭐
   sessionizer = wezterm.plugin.require("https://github.com/ElCapitanSponge/sessionizer.wezterm")
   
   -- 配置 sessionizer 项目目录 (Windows路径)
   local username = os.getenv("USERNAME") or "User"
   local projects = {
       wezterm.home_dir .. "\\Documents",
       wezterm.home_dir .. "\\Desktop", 
       "D:\\Projects",
       "D:\\Git",
       "C:\\Users\\<USER>\\source\\repos"
   }
   sessionizer.set_projects(projects)

   -- cmd-sender 插件配置
   cmd_sender.apply_to_config(config, {
      key = 'b',
      mods = 'ALT|CTRL',
      description = '向所有窗格发送命令'
   })

   -- sessionizer 插件配置
   sessionizer.configure(config)

   -- workspace_switcher 插件配置
   workspace_switcher.apply_to_config(config)

   -- 📁 设置 Windows 状态目录 (重要！)
   resurrect.state_manager.change_state_save_dir("C:\\Users\\<USER>\\AppData\\Local\\wezterm\\resurrect_states\\")

   -- 🕒 启用自动保存会话 (延长间隔以减少频繁保存)
   resurrect.state_manager.periodic_save({
      interval_seconds = 30 * 60,  -- 30分钟，减少频繁保存导致的闪动
      save_workspaces = true,
      save_windows = true,
      save_tabs = true,
   })

   -- 🚀 启动时恢复会话
   wezterm.on("gui-startup", resurrect.state_manager.resurrect_on_gui_startup)
   
   -- 标记插件已加载
   plugins_have_been_loaded = true
   wezterm.log_info('✅ 所有插件已成功加载，无重复')
end

-- 📊 工作区切换时的状态更新 (仅在插件加载时注册一次)
if not events_have_been_registered then
   wezterm.on("smart_workspace_switcher.workspace_switcher.chosen", function(window, workspace)
      local gui_win = window:gui_window()
      local base_path = string.gsub(workspace, "(.*[/\\])(.*)", "%2")
      gui_win:set_right_status(wezterm.format({
         { Foreground = { Color = "#c4a7e7" } },  -- Neapsix 紫色
         { Text = "📁 " .. base_path .. "  " },
      }))
   end)

   wezterm.on("smart_workspace_switcher.workspace_switcher.created", function(window, workspace)
      local gui_win = window:gui_window()
      local base_path = string.gsub(workspace, "(.*[/\\])(.*)", "%2")
      gui_win:set_right_status(wezterm.format({
         { Foreground = { Color = "#c4a7e7" } },  -- Neapsix 紫色
         { Text = "📁 " .. base_path .. "  " },
      }))
   end)

   -- 🔗 集成 resurrect 和 workspace_switcher (简化版，减少频繁保存)
   -- 工作区切换时自动保存状态 (仅在有插件时)
   if resurrect then
      wezterm.on("smart_workspace_switcher.workspace_switcher.selected", function(window, path, label)
         local workspace_state = resurrect.workspace_state
         if workspace_state and workspace_state.get_workspace_state then
            resurrect.state_manager.save_state(workspace_state.get_workspace_state())
         end
      end)
   end
   
   -- 标记事件已注册
   events_have_been_registered = true
end

return config.options

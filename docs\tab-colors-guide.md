# 🎨 标签页颜色系统使用指南

您的 WezTerm 现在支持**智能标签页颜色**功能！不同类型的终端会自动显示不同的颜色和图标。

---

## ✨ **功能特色**

### 🌈 **自动颜色识别**
系统会根据运行的进程自动为标签页分配相应的颜色：

| 终端类型 | 颜色主题 | 图标 | 说明 |
|----------|----------|------|------|
| **PowerShell 7** | 🔷 蓝色系 | 🔷 | 现代化 PowerShell |
| **Git Bash** | 🌿 绿色系 | 🐙 | Linux 风格环境 |
| **WSL Kali** | 💜 紫色系 | 🐧 | Linux 子系统 |
| **SSH 连接** | ❤️ 红色系 | 🌐 | 远程服务器 |
| **Command Prompt** | 💛 黄色系 | ⚫ | 传统 Windows 终端 |
| **其他/默认** | 🌸 玫瑰色系 | 🌸 | 通用终端 |

### 🎭 **视觉效果**
- **活动标签页**: 使用主色调，高对比度文字
- **非活动标签页**: 使用深色变体，柔和对比度
- **悬停效果**: 鼠标悬停时显示亮色突出效果

---

## 🚀 **立即体验**

### 1️⃣ **打开不同类型的终端**
```bash
Alt + 1    # PowerShell 7 (蓝色)
Alt + 2    # Git Bash (绿色)  
Alt + 3    # WSL Kali (紫色)
Alt + 4    # Command Prompt (黄色)
Alt + 5    # SSH 连接器 (红色)
```

### 2️⃣ **查看颜色预览**
```bash
Alt + P    # 打开标签页颜色预览器
```
可以查看所有可用的颜色主题和配色方案。

### 3️⃣ **运行测试**
运行专门的测试配置来体验颜色效果：
```bash
wezterm --config-file test-tab-colors.lua
```

---

## 🎨 **颜色主题详解**

### 🔷 **PowerShell 7 - 蓝色系**
- **活动颜色**: `#9ccfd8` (Foam 青色)
- **非活动颜色**: `#0f2832` (深蓝色背景)
- **设计理念**: 现代、专业、高效

### 🐙 **Git Bash - 绿色系**
- **活动颜色**: `#31748f` (Pine 松绿色)
- **非活动颜色**: `#0f2218` (深绿色背景)
- **设计理念**: 自然、开源、协作

### 🐧 **WSL Kali - 紫色系**
- **活动颜色**: `#c4a7e7` (Iris 紫色)
- **非活动颜色**: `#1f1232` (深紫色背景)  
- **设计理念**: 神秘、强大、安全

### 🌐 **SSH 连接 - 红色系**
- **活动颜色**: `#eb6f92` (Love 玫瑰红)
- **非活动颜色**: `#2f1218` (深红色背景)
- **设计理念**: 连接、重要、远程

### ⚫ **Command Prompt - 黄色系**
- **活动颜色**: `#f6c177` (Gold 金色)
- **非活动颜色**: `#2f2218` (深黄色背景)
- **设计理念**: 传统、稳定、经典

### 🌸 **默认 - 玫瑰色系**
- **活动颜色**: `#ebbcba` (Rose 玫瑰色)
- **非活动颜色**: `#2f1d1d` (深玫瑰色背景)
- **设计理念**: 温暖、通用、优雅

---

## 🔧 **自定义配置**

### 📝 **修改颜色主题**
编辑 `events/tab-colors.lua` 文件中的 `themes` 表：

```lua
-- 示例：修改 PowerShell 的颜色
powershell = {
   active = { bg_color = '#你的颜色', fg_color = palette.base },
   inactive = { bg_color = '#你的深色', fg_color = '#你的颜色' },
   icon = '🔷',
   accent = '#你的强调色'
},
```

### ➕ **添加新的终端类型**
在 `detect_tab_type` 函数中添加新的检测逻辑：

```lua
elseif string.find(process_name, '你的程序名') then
   return '你的类型名'
```

然后在 `themes` 表中添加对应的颜色配置。

### 🎨 **调整视觉效果**
修改 `format_tab_title` 函数来自定义：
- 标签页文字格式
- 图标显示方式  
- 悬停效果
- 长度截断规则

---

## 🛠️ **技术原理**

### 🧠 **智能检测机制**
系统通过以下方式检测终端类型：
1. **进程名称匹配**: 检查 `pwsh`, `bash`, `ssh`, `cmd` 等关键字
2. **工作目录分析**: 检测 WSL 路径特征
3. **优先级排序**: 确保准确识别不同类型

### 📊 **事件处理流程**
1. WezTerm 触发 `format-tab-title` 事件
2. 获取活动窗格信息
3. 检测进程类型并匹配颜色主题
4. 应用颜色和图标
5. 返回格式化的标签页显示

### 🔄 **性能优化**
- 缓存检测结果减少重复计算
- 轻量级字符串匹配算法
- 最小化内存占用

---

## 🐛 **故障排除**

### ❌ **颜色不显示**
1. 确认 WezTerm 版本支持彩色标签栏
2. 检查 `tab-colors.lua` 是否正确加载
3. 验证 `use_fancy_tab_bar = false` 设置

### 🎨 **颜色显示异常**
1. 检查终端色彩深度设置
2. 确认 WebGpu 前端正常工作
3. 验证显示器色彩配置

### 🔍 **类型检测错误**
1. 查看进程名称是否符合预期
2. 检查工作目录路径
3. 调整检测逻辑的匹配规则

### ⚡ **性能问题**
1. 减少复杂的字符串操作
2. 优化图标和文字长度
3. 检查事件处理频率

---

## 💡 **使用技巧**

### 🌟 **最佳实践**
1. **保持一致性**: 相同类型的工作使用相同颜色的终端
2. **快速识别**: 利用颜色快速定位目标标签页
3. **工作流优化**: 为不同项目使用不同类型的终端

### 🎯 **高效工作流**
```bash
# 开发环境设置
Alt + 1    # PowerShell 用于包管理和构建
Alt + 2    # Git Bash 用于版本控制
Alt + 3    # WSL 用于 Linux 工具
Alt + 5    # SSH 连接到部署服务器
```

### 🔗 **与其他功能结合**
- 配合 `Alt + M` 主题切换使用
- 结合 `Alt + ?` 背景选择器
- 利用 `Alt + P` 预览所有颜色主题

---

## 🎉 **享受彩色标签页！**

现在您的 WezTerm 标签页不仅功能强大，还非常美观！

### 📚 **相关文档**
- [主题系统指南](theme-guide.md)
- [快捷键参考](keybindings-reference.md)  
- [Neapsix 设置指南](neapsix-setup-guide.md)

### 🚀 **下一步**
- 尝试不同的终端组合
- 自定义您专属的颜色主题
- 分享您的配色方案

> 💡 **提示**: 使用 `Alt + P` 随时预览所有可用的标签页颜色主题！ 
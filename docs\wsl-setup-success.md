# 🎉 WSL Kali Linux 设置成功！

## ✅ **设置完成状态**

您的 WSL Kali Linux 已经成功配置并可以在 WezTerm 中使用！

### 📍 **安装详情**
- **WSL 发行版名称**: `kali-linux`
- **安装位置**: `F:\wsl\kali_root`
- **源文件**: `F:\wsl\kali_backup.tar`
- **默认用户**: `root`
- **WSL 版本**: 2

---

## 🚀 **如何使用**

### 🔥 **快速启动方式**

1. **数字键快速启动** (推荐)
   - 按 `Alt + 3` → 直接启动 Kali Linux

2. **启动器方式**
   - 按 `F3` → 选择 "🐧 WSL Kali (域)"

3. **高级快捷键**
   - 按 `Alt + Ctrl + T` → 启动 WSL 终端

### 🎯 **验证设置**

您可以通过以下方式验证设置是否正常：

```bash
# 在 PowerShell 中测试
wsl -d kali-linux whoami
# 应该显示: root

# 检查 WSL 状态
wsl --list --verbose
# 应该显示: kali-linux    Running/Stopped    2
```

---

## 🛠️ **WezTerm 配置详情**

### 📝 **域配置** (`config/domains.lua`)
```lua
{
   name = 'WSL:Kali',
   distribution = 'kali-linux',
   username = 'root',
   default_cwd = '/root',
   default_prog = { 'bash', '-l' },
}
```

### ⌨️ **快捷键配置**
- `Alt + 3` → 快速启动 Kali Linux
- `Alt + Ctrl + T` → WSL 终端
- `F3` → 启动器选择

---

## 🔧 **常用操作**

### 🐧 **在 Kali Linux 中**
```bash
# 更新系统
apt update && apt upgrade

# 安装常用工具
apt install vim nano curl wget git

# 创建普通用户 (可选)
adduser kali
usermod -aG sudo kali

# 设置默认用户为 kali (可选)
# 在 Windows 中运行: wsl -d kali-linux -u root usermod -aG sudo kali
```

### 🔄 **用户切换**
如果您想使用 `kali` 用户而不是 `root`：

1. 在 Kali 中创建用户：
   ```bash
   adduser kali
   usermod -aG sudo kali
   ```

2. 更新 `config/domains.lua`：
   ```lua
   username = 'kali',
   default_cwd = '/home/<USER>',
   ```

---

## 📂 **文件系统访问**

### 🔗 **Windows ↔ Linux 文件访问**

- **在 WSL 中访问 Windows 文件**:
  ```bash
  cd /mnt/c/Users/<USER>/  # C: 盘
  cd /mnt/f/               # F: 盘
  ```

- **在 Windows 中访问 WSL 文件**:
  ```
  \\wsl$\kali-linux\root\
  ```

### 💡 **工作流提示**
- 使用 `Alt + Ctrl + C` 复制当前路径
- 使用 `Alt + O` 在文件管理器中打开当前目录
- 项目文件建议放在 Windows 分区，通过 `/mnt/` 访问

---

## 🎨 **个性化设置**

### 🐚 **Shell 配置**
```bash
# 安装 zsh (可选)
apt install zsh

# 安装 oh-my-zsh
sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
```

### 🎯 **开发环境**
```bash
# Python 开发
apt install python3 python3-pip

# Node.js 开发
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
apt install nodejs

# Docker (如果需要)
apt install docker.io
```

---

## 🚨 **故障排除**

### ❌ **常见问题**

1. **WSL 启动失败**
   ```bash
   # 重启 WSL
   wsl --shutdown
   wsl -d kali-linux
   ```

2. **网络问题**
   ```bash
   # 重置网络
   wsl --shutdown
   # 重启 Windows 网络服务
   ```

3. **权限问题**
   ```bash
   # 检查用户权限
   whoami
   groups
   ```

### 🔧 **重置选项**
如果需要重新开始：
```bash
# 注销 WSL 发行版
wsl --unregister kali-linux

# 重新导入
wsl --import kali-linux F:\wsl\kali_root F:\wsl\kali_backup.tar
```

---

## 🎉 **恭喜！**

您现在拥有一个完全配置好的 WezTerm + WSL Kali Linux 开发环境！

### 🔥 **下一步建议**
1. 熟悉 `Alt + 3` 快速启动
2. 配置您喜欢的 shell 和工具
3. 设置开发环境 (Python, Node.js, etc.)
4. 探索 WezTerm 的其他功能

### 📚 **相关文档**
- [快捷键参考](keybindings-reference.md)
- [优化总结](optimization-summary.md)
- [WSL 设置指南](wsl-setup-guide.md)

---

> �� **享受您的高效开发环境！** 🚀 
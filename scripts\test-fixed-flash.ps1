#!/usr/bin/env pwsh

# WezTerm 闪动问题修复验证脚本

Write-Host "WezTerm 闪动问题修复验证" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan
Write-Host ""

# 1. 测试主配置文件语法
Write-Host "1. 测试主配置文件语法..." -ForegroundColor Yellow
$result1 = & wezterm --config-file wezterm.lua --help 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ wezterm.lua: 语法正确" -ForegroundColor Green
} else {
    Write-Host "❌ wezterm.lua: 语法错误" -ForegroundColor Red
    Write-Host "错误详情: $result1" -ForegroundColor Red
}

# 2. 测试修复配置文件语法
Write-Host "2. 测试修复配置文件语法..." -ForegroundColor Yellow
$result2 = & wezterm --config-file wezterm-fixed.lua --help 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ wezterm-fixed.lua: 语法正确" -ForegroundColor Green
} else {
    Write-Host "❌ wezterm-fixed.lua: 语法错误" -ForegroundColor Red
    Write-Host "错误详情: $result2" -ForegroundColor Red
}

# 3. 测试插件配置文件语法
Write-Host "3. 测试插件配置文件语法..." -ForegroundColor Yellow
$result3 = & wezterm --config-file test-plugins.lua --help 2>&1
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ test-plugins.lua: 语法正确" -ForegroundColor Green
} else {
    Write-Host "❌ test-plugins.lua: 语法错误" -ForegroundColor Red
    Write-Host "错误详情: $result3" -ForegroundColor Red
}

# 4. 检查Resurrect插件目录结构
Write-Host "4. 检查Resurrect插件目录结构..." -ForegroundColor Yellow
$resurrectDir = "$env:LOCALAPPDATA\wezterm\resurrect_states"
$workspaceDir = "$resurrectDir\workspace"
$windowDir = "$resurrectDir\window"
$tabDir = "$resurrectDir\tab"
$defaultJson = "$workspaceDir\default.json"

if (Test-Path $resurrectDir) {
    Write-Host "✅ 状态目录存在: $resurrectDir" -ForegroundColor Green
} else {
    Write-Host "❌ 状态目录缺失: $resurrectDir" -ForegroundColor Red
}

if (Test-Path $workspaceDir) {
    Write-Host "✅ Workspace 状态目录存在: $workspaceDir" -ForegroundColor Green
} else {
    Write-Host "❌ Workspace 状态目录缺失: $workspaceDir" -ForegroundColor Red
}

if (Test-Path $windowDir) {
    Write-Host "✅ Window 状态目录存在: $windowDir" -ForegroundColor Green
} else {
    Write-Host "❌ Window 状态目录缺失: $windowDir" -ForegroundColor Red
}

if (Test-Path $tabDir) {
    Write-Host "✅ Tab 状态目录存在: $tabDir" -ForegroundColor Green
} else {
    Write-Host "❌ Tab 状态目录缺失: $tabDir" -ForegroundColor Red
}

if (Test-Path $defaultJson) {
    $fileSize = (Get-Item $defaultJson).Length
    Write-Host "✅ 默认配置存在: default.json ($fileSize bytes)" -ForegroundColor Green
} else {
    Write-Host "❌ 默认配置缺失: default.json" -ForegroundColor Red
}

Write-Host ""
Write-Host "✅ 修复验证完成!" -ForegroundColor Green 
# Windows 多终端环境使用指南

本指南将帮助您在 WezTerm 中使用多个终端环境，包括 PowerShell、Git Bash、WSL 和 SSH 连接。

## 🚀 终端环境配置

### 已配置的终端环境

1. **🔷 PowerShell 7** (默认)
   - 路径: `D:\PowerShell-7.5.1-win-x64\pwsh.exe`
   - 功能: 现代化的 PowerShell，支持跨平台

2. **🔵 PowerShell (Legacy)**
   - 路径: `C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe`
   - 功能: Windows 传统 PowerShell

3. **🐙 Git Bash**
   - 路径: `D:\Git\bin\bash.exe`
   - 功能: Linux 风格的 Bash 环境，Git 工具集成

4. **⚫ Command Prompt**
   - 路径: `cmd.exe`
   - 功能: Windows 命令提示符

5. **🐧 Kali Linux (WSL)**
   - 发行版: `kali-linux`
   - 功能: Linux 环境，安全测试工具

## 🎯 快捷键操作

### 基本操作
- `F3`: 打开启动器 (显示所有可用终端)
- `F4`: 模糊搜索标签页
- `F5`: 工作区选择器

### 终端切换
- `Alt + T`: 新建默认终端标签页 (PowerShell 7)
- `Alt + Ctrl + T`: 新建 Kali Linux WSL 标签页

### SSH 连接
- `Alt + S`: 打开 SSH 连接选择器
- `Alt + Ctrl + S`: 快速 SSH 连接输入

### 窗格管理
- `Alt + \`: 垂直分割窗格
- `Alt + Ctrl + \`: 水平分割窗格
- `Alt + Enter`: 切换窗格缩放状态
- `Alt + W`: 关闭当前窗格

### 窗格导航
- `Alt + Ctrl + H/J/K/L`: 移动到左/下/上/右窗格

## 🔧 SSH 连接配置

### 添加SSH服务器

编辑 `config/ssh-connections.lua` 文件：

```lua
-- 基本SSH连接
ssh_manager:add_quick_connection(
    'my-server',           -- 连接名称
    'example.com',         -- 服务器地址
    'username',            -- 用户名
    22,                    -- 端口号（可选，默认22）
    nil                    -- 私钥文件路径（可选）
)

-- 使用私钥的SSH连接
ssh_manager:add_quick_connection(
    'vps-server',
    '*************',
    'root',
    22,
    os.getenv('USERPROFILE') .. '\\.ssh\\id_rsa'
)
```

### SSH 快速连接

1. **使用快捷键**: 按 `Alt + S` 打开连接选择器
2. **快速输入**: 按 `Alt + Ctrl + S` 输入连接字符串
3. **格式**: `user@hostname` 或 `user@hostname:port`

## 🐧 WSL 配置

### 检查 WSL 发行版

在 PowerShell 中运行：
```powershell
wsl --list --verbose
```

### 设置 Kali Linux

如果您的 Kali Linux 发行版名称不同，请修改 `config/domains.lua`：

```lua
wsl_domains = {
    {
        name = 'WSL:Kali',
        distribution = 'your-kali-distribution-name', -- 修改这里
        username = 'your-username',                   -- 修改用户名
        default_cwd = '/home/<USER>',          -- 修改主目录
        default_prog = { 'bash', '-l' },
    },
}
```

## 🎨 个性化配置

### 修改默认终端

在 `config/launch.lua` 中修改默认程序：

```lua
-- 设置为 Git Bash
options.default_prog = { 'D:\\Git\\bin\\bash.exe', '-l' }

-- 或设置为 PowerShell Legacy
options.default_prog = { 'C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe', '-NoLogo' }
```

### 添加自定义终端

在启动菜单中添加新的终端选项：

```lua
{ 
    label = '🔥 自定义终端', 
    args = { 'path\\to\\your\\terminal.exe', 'arguments' } 
},
```

## 🛠️ 故障排除

### 常见问题

1. **PowerShell 7 无法启动**
   - 检查路径: `D:\PowerShell-7.5.1-win-x64\pwsh.exe`
   - 确保 PowerShell 7 已正确安装

2. **Git Bash 无法启动**
   - 检查路径: `D:\Git\bin\bash.exe`
   - 确保 Git 已正确安装

3. **WSL 连接失败**
   - 确保 WSL 已启用: `wsl --install`
   - 检查发行版名称: `wsl --list`
   - 确保 Kali Linux 已安装

4. **SSH 连接问题**
   - 检查 SSH 客户端是否可用
   - 验证私钥权限和路径
   - 测试网络连接

### 日志查看

按 `F12` 打开调试覆盖层查看详细错误信息。

## 📚 进阶功能

### 会话管理
- 工作区: 按 `F5` 管理不同项目的终端布局
- 标签页: 使用 `Alt + [/]` 在标签页间切换

### 背景图片
- `Alt + /`: 随机背景
- `Alt + ,/.`: 循环切换背景
- `Alt + B`: 切换专注模式

### 字体缩放
- `Leader + F`: 进入字体调整模式 (Leader = Alt + Ctrl + Space)
- `K/J`: 增大/减小字体
- `R`: 重置字体大小

## 🔗 有用链接

- [WezTerm 官方文档](https://wezfurlong.org/wezterm/)
- [PowerShell 7 下载](https://github.com/PowerShell/PowerShell/releases)
- [Git for Windows](https://git-scm.com/download/win)
- [WSL 安装指南](https://docs.microsoft.com/en-us/windows/wsl/install) 
# WSL Kali Linux 诊断和修复脚本
# 针对自定义安装位置 F:\wsl\kali_root

Write-Host "WSL Kali Linux 诊断脚本" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 检查WSL版本
Write-Host "`n1. 检查WSL版本..." -ForegroundColor Yellow
wsl --version

# 检查WSL状态  
Write-Host "`n2. 检查WSL状态..." -ForegroundColor Yellow
wsl --status

# 检查已安装的发行版
Write-Host "`n3. 检查已安装的WSL发行版..." -ForegroundColor Yellow
wsl --list --verbose

# 检查Kali安装文件
Write-Host "`n4. 检查Kali安装文件..." -ForegroundColor Yellow
$kaliPath = "F:\wsl\kali_root"
if (Test-Path $kaliPath) {
    Write-Host "Kali安装目录存在: $kaliPath" -ForegroundColor Green
    Get-ChildItem $kaliPath | Format-Table Name, Length, LastWriteTime
} else {
    Write-Host "Kali安装目录不存在: $kaliPath" -ForegroundColor Red
}

# 检查ext4.vhdx文件
$vhdxFile = "$kaliPath\ext4.vhdx"
if (Test-Path $vhdxFile) {
    $fileInfo = Get-Item $vhdxFile
    Write-Host "找到VHDX文件: $($fileInfo.Name)" -ForegroundColor Green
    Write-Host "大小: $([math]::Round($fileInfo.Length / 1GB, 2)) GB" -ForegroundColor Gray
} else {
    Write-Host "未找到ext4.vhdx文件" -ForegroundColor Red
}

Write-Host "`n建议的修复步骤:" -ForegroundColor Yellow
Write-Host "================================" -ForegroundColor Cyan

Write-Host "`n方案1: 手动导入Kali Linux" -ForegroundColor Green
Write-Host "运行以下命令:" -ForegroundColor White
Write-Host "wsl --set-default-version 2" -ForegroundColor Gray
Write-Host "wsl --import kali-linux F:\wsl\kali_root F:\wsl\kali_root\ext4.vhdx --vhd" -ForegroundColor Gray

Write-Host "`n方案2: 在WezTerm中使用直接启动" -ForegroundColor Green
Write-Host "按F3打开启动器，选择 'WSL Kali (直接)' 或 'WSL Kali (通用)'" -ForegroundColor White

Write-Host "`n测试连接..." -ForegroundColor Yellow
wsl --exec echo "WSL连接测试"

$autoFix = Read-Host "`n是否尝试自动导入Kali Linux? (y/N)"
if ($autoFix -eq 'y' -or $autoFix -eq 'Y') {
    Write-Host "`n尝试自动修复..." -ForegroundColor Yellow
    wsl --set-default-version 2
    wsl --import kali-linux F:\wsl\kali_root F:\wsl\kali_root\ext4.vhdx --vhd
    
    Write-Host "验证结果:" -ForegroundColor White
    wsl --list --verbose
}

Write-Host "`n诊断完成！重启WezTerm来测试配置。" -ForegroundColor Green 
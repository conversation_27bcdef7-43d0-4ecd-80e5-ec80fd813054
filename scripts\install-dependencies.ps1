# 🔧 WezTerm 推荐插件依赖安装脚本
# 此脚本将安装插件所需的依赖项

Write-Host "🔌 WezTerm 推荐插件依赖安装程序" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan
Write-Host ""

# 检查是否以管理员身份运行
function Test-Admin {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 安装 Scoop (如果未安装)
function Install-Scoop {
    Write-Host "📦 检查 Scoop 包管理器..." -ForegroundColor Yellow
    
    if (!(Get-Command scoop -ErrorAction SilentlyContinue)) {
        Write-Host "⬇️ 安装 Scoop..." -ForegroundColor Green
        try {
            Set-ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
            irm get.scoop.sh | iex
            Write-Host "✅ Scoop 安装成功!" -ForegroundColor Green
        } catch {
            Write-Host "❌ Scoop 安装失败: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "✅ Scoop 已安装" -ForegroundColor Green
    }
    return $true
}

# 安装 zoxide (workspace_switcher 依赖)
function Install-Zoxide {
    Write-Host "🧭 安装 zoxide (智能目录跳转)..." -ForegroundColor Yellow
    
    if (!(Get-Command zoxide -ErrorAction SilentlyContinue)) {
        try {
            # 尝试使用 winget
            if (Get-Command winget -ErrorAction SilentlyContinue) {
                Write-Host "⬇️ 使用 winget 安装 zoxide..." -ForegroundColor Green
                winget install ajeetdsouza.zoxide
            } 
            # 备选：使用 scoop
            elseif (Get-Command scoop -ErrorAction SilentlyContinue) {
                Write-Host "⬇️ 使用 scoop 安装 zoxide..." -ForegroundColor Green  
                scoop install zoxide
            }
            else {
                Write-Host "❌ 未找到 winget 或 scoop，请手动安装 zoxide" -ForegroundColor Red
                Write-Host "   访问: https://github.com/ajeetdsouza/zoxide#installation" -ForegroundColor Yellow
                return $false
            }
            
            Write-Host "✅ zoxide 安装成功!" -ForegroundColor Green
            Write-Host "💡 请重启终端以使 zoxide 生效" -ForegroundColor Yellow
        } catch {
            Write-Host "❌ zoxide 安装失败: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "✅ zoxide 已安装" -ForegroundColor Green
    }
    return $true
}

# 创建必要的目录
function Create-Directories {
    Write-Host "📁 创建必要目录..." -ForegroundColor Yellow
    
    $dirs = @(
        "$env:LOCALAPPDATA\wezterm\resurrect_states",
        "$env:APPDATA\wezterm\plugins"
    )
    
    foreach ($dir in $dirs) {
        if (!(Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Host "✅ 创建目录: $dir" -ForegroundColor Green
        } else {
            Write-Host "✅ 目录已存在: $dir" -ForegroundColor Green
        }
    }
}

# 配置 PowerShell 集成
function Configure-PowerShell {
    Write-Host "⚙️ 配置 PowerShell 集成..." -ForegroundColor Yellow
    
    $profilePath = $PROFILE
    $profileDir = Split-Path $profilePath -Parent
    
    if (!(Test-Path $profileDir)) {
        New-Item -ItemType Directory -Path $profileDir -Force | Out-Null
    }
    
    # 检查是否已配置 zoxide
    if (Test-Path $profilePath) {
        $content = Get-Content $profilePath -Raw
        if ($content -notmatch "zoxide init") {
            Add-Content $profilePath "`n# zoxide 初始化`nInvoke-Expression (& { (zoxide init powershell | Out-String) })"
            Write-Host "✅ 已添加 zoxide 到 PowerShell 配置" -ForegroundColor Green
        } else {
            Write-Host "✅ zoxide 已在 PowerShell 中配置" -ForegroundColor Green
        }
    } else {
        Set-Content $profilePath "# zoxide 初始化`nInvoke-Expression (& { (zoxide init powershell | Out-String) })"
        Write-Host "✅ 已创建 PowerShell 配置文件并添加 zoxide" -ForegroundColor Green
    }
}

# 主安装流程
function Main {
    Write-Host "🚀 开始安装依赖..." -ForegroundColor Cyan
    Write-Host ""
    
    # 1. 安装 Scoop (如果需要)
    if (!(Install-Scoop)) {
        Write-Host "❌ 无法安装 Scoop，将尝试其他方法" -ForegroundColor Yellow
    }
    
    # 2. 安装 zoxide
    if (!(Install-Zoxide)) {
        Write-Host "❌ zoxide 安装失败，智能工作区切换功能将不可用" -ForegroundColor Red
    }
    
    # 3. 创建目录
    Create-Directories
    
    # 4. 配置 PowerShell
    Configure-PowerShell
    
    Write-Host ""
    Write-Host "🎉 依赖安装完成!" -ForegroundColor Green
    Write-Host "📋 安装摘要:" -ForegroundColor Cyan
    Write-Host "  ✅ 目录结构已创建" -ForegroundColor Green
    Write-Host "  ✅ zoxide 已安装 (智能目录跳转)" -ForegroundColor Green
    Write-Host "  ✅ PowerShell 集成已配置" -ForegroundColor Green
    Write-Host ""
    Write-Host "🔄 下一步:" -ForegroundColor Yellow
    Write-Host "  1. 重启 PowerShell/WezTerm" -ForegroundColor White
    Write-Host "  2. 运行测试: wezterm --config-file test-plugins.lua" -ForegroundColor White
    Write-Host "  3. 按 Alt+H 查看插件使用帮助" -ForegroundColor White
    Write-Host ""
    Write-Host "🔗 插件功能:" -ForegroundColor Cyan
    Write-Host "  📦 cmd-sender: Alt+Ctrl+B (批量命令)" -ForegroundColor White
    Write-Host "  🔄 resurrect: Leader+R/S (会话管理)" -ForegroundColor White
    Write-Host "  🏢 workspace_switcher: Alt+A (工作区切换)" -ForegroundColor White
    Write-Host "  📁 sessionizer: Leader+F (项目切换)" -ForegroundColor White
}

# 运行主程序
Main 
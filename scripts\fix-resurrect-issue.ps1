# 🔧 Resurrect 插件状态保存问题修复脚本
Write-Host "🔧 修复 Resurrect 插件状态保存问题" -ForegroundColor Cyan
Write-Host "======================================" -ForegroundColor Cyan
Write-Host ""

# 获取用户名和目录路径
$username = $env:USERNAME
$resurrectDir = "$env:LOCALAPPDATA\wezterm\resurrect_states"

Write-Host "📁 用户: $username" -ForegroundColor Yellow
Write-Host "📁 状态目录: $resurrectDir" -ForegroundColor Yellow
Write-Host ""

# 1. 删除并重新创建目录结构
Write-Host "🗑️ 重新创建 resurrect 目录结构..." -ForegroundColor Yellow

if (Test-Path $resurrectDir) {
    Write-Host "删除现有目录..." -ForegroundColor Gray
    Remove-Item $resurrectDir -Recurse -Force
}

# 创建主目录和子目录
$subDirs = @("workspace", "window", "tab")
foreach ($subDir in $subDirs) {
    $fullPath = Join-Path $resurrectDir $subDir
    New-Item -ItemType Directory -Path $fullPath -Force | Out-Null
    Write-Host "✅ 创建目录: $fullPath" -ForegroundColor Green
}

# 2. 设置目录权限
Write-Host ""
Write-Host "🔒 设置目录权限..." -ForegroundColor Yellow
try {
    # 获取当前用户的访问权限
    $acl = Get-Acl $resurrectDir
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule($username, "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    Set-Acl -Path $resurrectDir -AclObject $acl
    Write-Host "✅ 目录权限设置完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 权限设置警告: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 3. 测试写入权限
Write-Host ""
Write-Host "📝 测试写入权限..." -ForegroundColor Yellow
foreach ($subDir in $subDirs) {
    $testDir = Join-Path $resurrectDir $subDir
    $testFile = Join-Path $testDir "test.json"
    
    try {
        $testData = @{
            test = "data"
            timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
            user = $username
        } | ConvertTo-Json
        
        $testData | Out-File -FilePath $testFile -Force -Encoding UTF8
        
        if (Test-Path $testFile) {
            $fileSize = (Get-Item $testFile).Length
            Write-Host "✅ $subDir 目录写入测试成功 (文件大小: $fileSize 字节)" -ForegroundColor Green
            Remove-Item $testFile -Force
        } else {
            Write-Host "❌ $subDir 目录写入失败" -ForegroundColor Red
        }
    } catch {
        Write-Host "❌ $subDir 目录写入错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# 4. 创建默认配置文件
Write-Host ""
Write-Host "⚙️ 创建默认配置..." -ForegroundColor Yellow

$defaultWorkspace = @{
    format_version = "1.0"
    timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    workspace_name = "default"
    tabs = @()
} | ConvertTo-Json -Depth 10

$defaultWorkspaceFile = Join-Path (Join-Path $resurrectDir "workspace") "default.json"
try {
    $defaultWorkspace | Out-File -FilePath $defaultWorkspaceFile -Force -Encoding UTF8
    Write-Host "✅ 创建默认工作区配置: default.json" -ForegroundColor Green
} catch {
    Write-Host "❌ 创建默认工作区配置失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 5. 验证修复结果
Write-Host ""
Write-Host "🔍 验证修复结果..." -ForegroundColor Yellow

$issues = 0
foreach ($subDir in $subDirs) {
    $dirPath = Join-Path $resurrectDir $subDir
    if (Test-Path $dirPath) {
        Write-Host "✅ $subDir 目录存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $subDir 目录缺失" -ForegroundColor Red
        $issues++
    }
}

if (Test-Path $defaultWorkspaceFile) {
    Write-Host "✅ 默认工作区配置存在" -ForegroundColor Green
} else {
    Write-Host "❌ 默认工作区配置缺失" -ForegroundColor Red
    $issues++
}

Write-Host ""
if ($issues -eq 0) {
    Write-Host "🎉 所有问题已修复!" -ForegroundColor Green
    Write-Host "📋 下一步:" -ForegroundColor Cyan
    Write-Host "1. 重启 WezTerm" -ForegroundColor White
    Write-Host "2. 测试 resurrect 插件功能:" -ForegroundColor White
    Write-Host "   - Leader + S: 保存状态" -ForegroundColor White
    Write-Host "   - Leader + R: 恢复状态" -ForegroundColor White
} else {
    Write-Host "⚠️ 还有 $issues 个问题需要手动处理" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📁 目录结构已创建:" -ForegroundColor Cyan
if (Test-Path $resurrectDir) {
    Get-ChildItem $resurrectDir -Recurse | ForEach-Object {
        Write-Host "  $($_.FullName)" -ForegroundColor Gray
    }
} 
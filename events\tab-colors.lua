local wezterm = require('wezterm')

-- 标签页颜色配置
local TabColors = {}

-- <PERSON><PERSON><PERSON> 调色板
local palette = {
   base = '#191724',
   surface = '#1f1d2e',
   overlay = '#26233a',
   muted = '#6e6a86',
   subtle = '#908caa',
   text = '#e0def4',
   love = '#eb6f92',
   gold = '#f6c177',
   rose = '#ebbcba',
   pine = '#31748f',
   foam = '#9ccfd8',
   iris = '#c4a7e7',
   highlight_low = '#21202e',
   highlight_med = '#403d52',
   highlight_high = '#524f67',
}

-- 为不同类型的标签页定义颜色主题
TabColors.themes = {
   -- PowerShell 7 - 蓝色系
   powershell = {
      active = { bg_color = palette.foam, fg_color = palette.base },
      inactive = { bg_color = '#0f2832', fg_color = palette.foam },
      icon = '🔷',
      accent = palette.foam
   },
   
   -- Git Bash - 绿色系  
   gitbash = {
      active = { bg_color = palette.pine, fg_color = palette.base },
      inactive = { bg_color = '#0f2218', fg_color = palette.pine },
      icon = '🐙',
      accent = palette.pine
   },
   
   -- WSL Kali - 紫色系
   wsl = {
      active = { bg_color = palette.iris, fg_color = palette.base },
      inactive = { bg_color = '#1f1232', fg_color = palette.iris },
      icon = '🐧',
      accent = palette.iris
   },
   
   -- SSH 连接 - 红色系
   ssh = {
      active = { bg_color = palette.love, fg_color = palette.base },
      inactive = { bg_color = '#2f1218', fg_color = palette.love },
      icon = '🌐',
      accent = palette.love
   },
   
   -- 命令提示符 - 黄色系
   cmd = {
      active = { bg_color = palette.gold, fg_color = palette.base },
      inactive = { bg_color = '#2f2218', fg_color = palette.gold },
      icon = '⚫',
      accent = palette.gold
   },
   
   -- 默认 - 玫瑰色系
   default = {
      active = { bg_color = palette.rose, fg_color = palette.base },
      inactive = { bg_color = '#2f1d1d', fg_color = palette.rose },
      icon = '🌸',
      accent = palette.rose
   },
}

-- 根据进程名称或路径检测标签页类型
function TabColors.detect_tab_type(pane)
   -- 尝试获取进程信息，使用更安全的方法
   -- local process_name = nil
   -- local process_info = nil

   -- if pane and type(pane.get_foreground_process_info) == 'function' then
   --    process_info = pane:get_foreground_process_info()
   -- end
   
   -- if process_info and process_info.name then
   --    process_name = process_info.name
   -- elseif process_info and process_info.executable then
   --    process_name = process_info.executable
   -- elseif pane and type(pane.get_foreground_process_name) == 'function' then -- 新增回退到 get_foreground_process_name
   --    process_name = pane:get_foreground_process_name()
   -- else
   --    -- 回退方案：从标签标题推断
   --    local tab = pane:tab()
   --    local title = tab:get_title()
   --    if title and string.find(string.lower(title), 'powershell') then
   --       return 'powershell'
   --    elseif title and string.find(string.lower(title), 'bash') then
   --       return 'gitbash'
   --    elseif title and string.find(string.lower(title), 'cmd') then
   --       return 'cmd'
   --    end
   --    return 'default'
   -- end
   
   -- local current_working_dir = pane:get_current_working_dir()
   
   -- if not process_name then
   --    return 'default'
   -- end
   
   -- -- 转换为小写便于匹配
   -- process_name = string.lower(process_name)
   
   -- -- 检测不同类型的终端
   -- if string.find(process_name, 'pwsh') or string.find(process_name, 'powershell') then
   --    return 'powershell'
   -- elseif string.find(process_name, 'bash') then
   --    return 'gitbash'
   -- elseif string.find(process_name, 'ssh') then
   --    return 'ssh'
   -- elseif string.find(process_name, 'cmd') then
   --    return 'cmd'
   -- elseif current_working_dir and string.find(tostring(current_working_dir), 'wsl') then
   --    return 'wsl'
   -- else
   --    return 'default'
   -- end
   return 'default' -- 总是返回 default 以进行测试
end

-- 简化的标签页标题格式化器，重写原有的复杂版本
function TabColors.format_tab_title(tab, tabs, panes, config, hover, max_width)
   local pane = tab.active_pane
   -- local tab_type = TabColors.detect_tab_type(pane) -- 暂时禁用复杂的类型检测
   local tab_type = 'default' -- 总是使用 default 类型
   local theme = TabColors.themes[tab_type] or TabColors.themes.default
   
   -- 安全获取进程名称和标题
   -- local process_name = 'unknown'
   -- local process_info = nil

   -- if pane and type(pane.get_foreground_process_info) == 'function' then
   --    process_info = pane:get_foreground_process_info()
   -- end

   -- if process_info and process_info.name then
   --    process_name = process_info.name
   -- elseif process_info and process_info.executable then
   --    process_name = process_info.executable
   -- elseif pane and type(pane.get_foreground_process_name) == 'function' then -- 新增回退到 get_foreground_process_name
   --    process_name = pane:get_foreground_process_name()
   -- end
   
   -- local basename = string.gsub(process_name, '(.*[/\])(.*)', '%2')
   -- basename = basename:gsub('%.exe$', '') -- 移除 .exe 扩展名
   
   -- 获取标签页标题
   local tab_title = tab.tab_title and #tab.tab_title > 0 and tab.tab_title or 'Default Tab' -- 使用固定标题
   
   -- 构建完整标题
   local title = string.format(' %s %s ', theme.icon, tab_title)
   
   -- 如果标题太长，进行截断
   if #title > max_width - 2 then
      title = string.sub(title, 1, max_width - 5) .. '...'
   end
   
   -- 确定颜色状态
   local is_active = tab.is_active
   local is_hover = hover
   
   local bg_color, fg_color
   if is_active then
      bg_color = theme.active.bg_color
      fg_color = theme.active.fg_color
   else
      bg_color = theme.inactive.bg_color
      fg_color = theme.inactive.fg_color
      -- 悬停效果
      if is_hover then
         bg_color = theme.accent
         fg_color = palette.base
      end
   end
   
   return {
      { Background = { Color = bg_color } },
      { Foreground = { Color = fg_color } },
      { Text = title },
   }
end

-- 设置事件监听器，重写现有的标签标题格式化
function TabColors.setup()
   -- 重新定义标签标题格式化事件
   wezterm.on('format-tab-title', function(tab, tabs, panes, config, hover, max_width)
      return TabColors.format_tab_title(tab, tabs, panes, config, hover, max_width)
   end)
   
   -- 添加窗口标题格式化
   wezterm.on('format-window-title', function(tab, pane, tabs, panes, config)
      -- local tab_type = TabColors.detect_tab_type(pane)
      local tab_type = 'default' -- 总是使用 default 类型
      local theme = TabColors.themes[tab_type] or TabColors.themes.default
      
      -- 安全获取进程名称
      -- local process_name = 'WezTerm'
      -- local process_info = nil

      -- if pane and type(pane.get_foreground_process_info) == 'function' then
      --    process_info = pane:get_foreground_process_info()
      -- end
      
      -- if process_info and process_info.name then
      --    process_name = process_info.name
      -- elseif process_info and process_info.executable then
      --    process_name = process_info.executable
      -- elseif pane and type(pane.get_foreground_process_name) == 'function' then -- 新增回退到 get_foreground_process_name
      --    process_name = pane:get_foreground_process_name()
      -- end
      
      -- local basename = string.gsub(process_name, '(.*[/\])(.*)', '%2')
      -- basename = basename:gsub('%.exe$', '')
      
      return string.format('%s %s - WezTerm', theme.icon, 'Default Window') -- 使用固定窗口标题
   end)
   
   wezterm.log_info('TabColors: 彩色标签页系统已启用 (简化版)')
end

-- 获取当前可用的颜色主题
function TabColors.get_themes()
   local themes = {}
   for name, theme in pairs(TabColors.themes) do
      table.insert(themes, {
         name = name,
         icon = theme.icon,
         active_color = theme.active.bg_color,
         inactive_color = theme.inactive.bg_color,
         accent = theme.accent
      })
   end
   return themes
end

-- 创建颜色预览功能
function TabColors.create_color_preview()
   return wezterm.action_callback(function(window, pane)
      local choices = {}
      for name, theme in pairs(TabColors.themes) do
         table.insert(choices, {
            id = name,
            label = string.format('%s %s 主题 - 活动: %s, 非活动: %s', 
               theme.icon, name, 
               theme.active.bg_color, 
               theme.inactive.bg_color)
         })
      end
      
      window:perform_action(wezterm.action.InputSelector({
         title = '🎨 标签页颜色主题预览',
         choices = choices,
         fuzzy = true,
         fuzzy_description = '搜索颜色主题: ',
         action = wezterm.action_callback(function(inner_window, inner_pane, id, label)
            if id then
               inner_window:toast_notification('WezTerm 颜色主题', '预览: ' .. label, nil, 3000)
            end
         end)
      }), pane)
   end)
end

return TabColors
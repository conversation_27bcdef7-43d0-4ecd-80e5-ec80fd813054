# WezTerm 多终端环境验证脚本
# 运行此脚本来检查您的终端环境配置

Write-Host "🔍 WezTerm 多终端环境配置检查" -ForegroundColor Cyan
Write-Host "=" * 50

# 检查 PowerShell 7
Write-Host "`n🔷 检查 PowerShell 7..." -ForegroundColor Blue
$pwsh7Path = "D:\PowerShell-7.5.1-win-x64\pwsh.exe"
if (Test-Path $pwsh7Path) {
    Write-Host "✅ PowerShell 7 已找到: $pwsh7Path" -ForegroundColor Green
    try {
        $version = & $pwsh7Path -Command '$PSVersionTable.PSVersion.ToString()'
        Write-Host "   版本: $version" -ForegroundColor Gray
    } catch {
        Write-Host "⚠️  无法获取版本信息" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ PowerShell 7 未找到: $pwsh7Path" -ForegroundColor Red
    Write-Host "   请检查安装路径或从 https://github.com/PowerShell/PowerShell/releases 下载" -ForegroundColor Yellow
}

# 检查传统 PowerShell
Write-Host "`n🔵 检查传统 PowerShell..." -ForegroundColor Blue
$powershellPath = "C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe"
if (Test-Path $powershellPath) {
    Write-Host "✅ 传统 PowerShell 已找到: $powershellPath" -ForegroundColor Green
    try {
        $version = & $powershellPath -Command '$PSVersionTable.PSVersion.ToString()'
        Write-Host "   版本: $version" -ForegroundColor Gray
    } catch {
        Write-Host "⚠️  无法获取版本信息" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ 传统 PowerShell 未找到" -ForegroundColor Red
}

# 检查 Git Bash
Write-Host "`n🐙 检查 Git Bash..." -ForegroundColor Blue
$gitBashPath = "D:\Git\bin\bash.exe"
if (Test-Path $gitBashPath) {
    Write-Host "✅ Git Bash 已找到: $gitBashPath" -ForegroundColor Green
    try {
        $gitVersion = & "D:\Git\cmd\git.exe" --version 2>$null
        Write-Host "   Git 版本: $gitVersion" -ForegroundColor Gray
    } catch {
        Write-Host "⚠️  无法获取 Git 版本信息" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Git Bash 未找到: $gitBashPath" -ForegroundColor Red
    Write-Host "   请检查 Git 安装路径或从 https://git-scm.com/download/win 下载" -ForegroundColor Yellow
}

# 检查 WSL
Write-Host "`n🐧 检查 WSL..." -ForegroundColor Blue
try {
    $wslList = wsl --list --quiet 2>$null
    if ($wslList) {
        Write-Host "✅ WSL 已安装，可用发行版:" -ForegroundColor Green
        $wslList | ForEach-Object {
            Write-Host "   - $_" -ForegroundColor Gray
        }
        
        # 检查 Kali Linux
        if ($wslList -contains "kali-linux") {
            Write-Host "✅ Kali Linux 发行版已找到" -ForegroundColor Green
        } else {
            Write-Host "⚠️  未找到 'kali-linux' 发行版，请检查名称或安装" -ForegroundColor Yellow
            Write-Host "   使用 'wsl --install -d kali-linux' 安装" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ WSL 未安装或无可用发行版" -ForegroundColor Red
        Write-Host "   使用 'wsl --install' 安装" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ WSL 命令不可用" -ForegroundColor Red
    Write-Host "   请确保 WSL 已启用并重启系统" -ForegroundColor Yellow
}

# 检查 SSH 客户端
Write-Host "`n🌐 检查 SSH 客户端..." -ForegroundColor Blue
try {
    $sshVersion = ssh -V 2>&1
    Write-Host "✅ SSH 客户端可用: $sshVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ SSH 客户端不可用" -ForegroundColor Red
    Write-Host "   SSH 客户端应该包含在 Windows 10/11 中" -ForegroundColor Yellow
}

# 检查 WezTerm 配置目录
Write-Host "`n⚙️  检查 WezTerm 配置..." -ForegroundColor Blue
$configDir = "$env:USERPROFILE\.config\wezterm"
if (Test-Path $configDir) {
    Write-Host "✅ 配置目录已找到: $configDir" -ForegroundColor Green
    
    # 检查关键配置文件
    $configFiles = @(
        "wezterm.lua",
        "config\launch.lua",
        "config\domains.lua",
        "config\ssh-connections.lua",
        "utils\ssh-manager.lua"
    )
    
    foreach ($file in $configFiles) {
        $filePath = Join-Path $configDir $file
        if (Test-Path $filePath) {
            Write-Host "   ✅ $file" -ForegroundColor Green
        } else {
            Write-Host "   ❌ $file" -ForegroundColor Red
        }
    }
} else {
    Write-Host "❌ WezTerm 配置目录未找到: $configDir" -ForegroundColor Red
}

# 总结
Write-Host "`n📋 配置建议:" -ForegroundColor Cyan
Write-Host "1. 根据上述检查结果修正任何❌的项目" -ForegroundColor White
Write-Host "2. 在 config\ssh-connections.lua 中配置您的 SSH 服务器" -ForegroundColor White
Write-Host "3. 根据实际的 WSL 发行版名称调整 config\domains.lua" -ForegroundColor White
Write-Host "4. 测试各个终端环境是否正常工作" -ForegroundColor White

Write-Host "`n🚀 配置完成后，按 F3 在 WezTerm 中查看所有可用终端!" -ForegroundColor Green
Write-Host "=" * 50 
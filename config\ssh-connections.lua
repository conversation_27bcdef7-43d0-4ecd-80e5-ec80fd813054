-- SSH连接配置文件
-- 在这里配置您的SSH服务器连接

local SSHManager = require('utils.ssh-manager')

-- 创建SSH管理器实例
local ssh_manager = SSHManager:new()

-- 配置SSH连接示例
-- 取消注释并修改以下配置以添加您的SSH服务器

-- 示例1: 基本SSH连接
-- ssh_manager:add_quick_connection(
--     'my-server',           -- 连接名称
--     'example.com',         -- 服务器地址
--     'username',            -- 用户名
--     22,                    -- 端口号（可选，默认22）
--     nil                    -- 私钥文件路径（可选）
-- )

-- 示例2: 使用私钥的SSH连接
-- ssh_manager:add_quick_connection(
--     'vps-server',
--     '*************',
--     'root',
--     22,
--     os.getenv('USERPROFILE') .. '\\.ssh\\id_rsa'
-- )

-- 示例3: 高级SSH连接配置
-- ssh_manager:add_connection({
--     name = 'production-server',
--     remote_address = 'prod.company.com',
--     username = 'deploy',
--     ssh_option = {
--         Port = "2222",
--         IdentityFile = os.getenv('USERPROFILE') .. '\\.ssh\\prod_key',
--         StrictHostKeyChecking = 'no',
--         UserKnownHostsFile = '/dev/null'
--     },
--     multiplexing = 'None',
--     default_prog = { 'zsh', '-l' },
--     assume_shell = 'Posix',
-- })

-- 本地WSL SSH连接（暂时注释掉以避免配置错误）
-- 如果需要SSH到WSL，请先确保WSL开启了SSH服务
-- ssh_manager:add_connection({
--     name = 'wsl-kali',
--     remote_address = 'localhost',
--     username = 'kali',
--     ssh_option = {
--         Port = "22",
--     },
--     multiplexing = 'None',
--     default_prog = { 'bash', '-l' },
--     assume_shell = 'Posix',
-- })

return ssh_manager 
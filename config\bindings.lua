local wezterm = require('wezterm')
local platform = require('utils.platform')
local backdrops = require('utils.backdrops')
local theme_switcher = require('utils.theme-switcher')
local tab_colors = require('events.tab-colors')
local act = wezterm.action

-- 引入SSH管理器
local ssh_connections = require('config.ssh-connections')

local mod = {}

if platform.is_mac then
   mod.SUPER = 'SUPER'
   mod.SUPER_REV = 'SUPER|CTRL'
elseif platform.is_win or platform.is_linux then
   mod.SUPER = 'ALT' -- to not conflict with Windows key shortcuts
   mod.SUPER_REV = 'ALT|CTRL'
end

-- 工具函数 --
local function copy_current_working_dir(window, pane)
   local uri = pane:get_current_working_dir()
   if uri then
      local cwd = uri.file_path or uri.path or tostring(uri)
      window:copy_to_clipboard(cwd)
      window:toast_notification('WezTerm', '路径已复制: ' .. cwd, nil, 2000)
   end
end

local function open_in_explorer(window, pane)
   local uri = pane:get_current_working_dir()
   if uri then
      local cwd = uri.file_path or uri.path or tostring(uri)
      wezterm.open_with(cwd)
   end
end

local function restart_current_terminal(window, pane)
   local tab = window:active_tab()
   if tab then
      tab:activate()
      window:perform_action(act.SpawnTab('CurrentPaneDomain'), pane)
      window:perform_action(act.CloseCurrentTab({ confirm = false }), pane)
   end
end

-- stylua: ignore
local keys = {
   -- ========== 功能键 ========== --
   { key = 'F1', mods = 'NONE', action = 'ActivateCopyMode' },
   { key = 'F2', mods = 'NONE', action = act.ActivateCommandPalette },
   { key = 'F3', mods = 'NONE', action = act.ShowLauncher },
   { key = 'F4', mods = 'NONE', action = act.ShowLauncherArgs({ flags = 'FUZZY|TABS' }) },
   { key = 'F5', mods = 'NONE', action = act.ShowLauncherArgs({ flags = 'FUZZY|WORKSPACES' }) },
   { key = 'F11', mods = 'NONE', action = act.ToggleFullScreen },
   { key = 'F12', mods = 'NONE', action = act.ShowDebugOverlay },

   -- ========== 数字键快速启动终端 ========== --
   { 
      key = '1', 
      mods = mod.SUPER, 
      action = act.SpawnCommandInNewTab({
         label = 'PowerShell 7',
         args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo' }
      })
   },
   { 
      key = '2', 
      mods = mod.SUPER, 
      action = act.SpawnCommandInNewTab({
         label = 'Git Bash',
         args = { 'D:\\Git\\bin\\bash.exe', '-l' },
         cwd = os.getenv('USERPROFILE')
      })
   },
   { 
      key = '3', 
      mods = mod.SUPER, 
      action = act.SpawnTab({ DomainName = 'WSL:Kali' })
   },
   { 
      key = '4', 
      mods = mod.SUPER, 
      action = act.SpawnCommandInNewTab({
         label = 'Command Prompt',
         args = { 'cmd.exe' }
      })
   },
   {
      key = '5',
      mods = mod.SUPER,
      action = wezterm.action_callback(function(window, _pane)
         ssh_connections:show_connection_dialog(window)
      end),
   },

   -- ========== 基础层 (Alt + 单键) ========== --
   { key = 't', mods = mod.SUPER, action = act.SpawnTab('DefaultDomain') },
   { key = 'w', mods = mod.SUPER, action = act.CloseCurrentPane({ confirm = false }) },
   { key = 'n', mods = mod.SUPER, action = act.SpawnWindow },
   { key = 'f', mods = mod.SUPER, action = act.Search({ CaseInSensitiveString = '' }) },
   { key = 's', mods = mod.SUPER, action = wezterm.action_callback(function(window, _pane)
      ssh_connections:show_connection_dialog(window)
   end)},
   { key = 'b', mods = mod.SUPER, action = wezterm.action_callback(function(window, _pane)
      backdrops:toggle_focus(window)
   end)},
   { 
      key = 'r', 
      mods = mod.SUPER, 
      action = wezterm.action_callback(restart_current_terminal)
   },
   { 
      key = 'o', 
      mods = mod.SUPER, 
      action = wezterm.action_callback(open_in_explorer)
   },
   -- 保持原有的 Git Bash 快捷键以兼容
   { 
      key = 'g', 
      mods = mod.SUPER, 
      action = act.SpawnCommandInNewTab({
         args = { 'D:\\Git\\bin\\bash.exe', '-l' },
         cwd = os.getenv('USERPROFILE')
      })
   },

   -- ========== 高级层 (Alt + Ctrl + 单键) ========== --
   { key = 't', mods = mod.SUPER_REV, action = act.SpawnTab({ DomainName = 'WSL:Kali' }) },
   { key = 'w', mods = mod.SUPER_REV, action = act.CloseCurrentTab({ confirm = false }) },
   { key = 's', mods = mod.SUPER_REV, action = act.PromptInputLine({
      description = '快速SSH连接 (格式: user@host[:port]): ',
      action = wezterm.action_callback(function(window, pane, line)
         if line then
            window:perform_action(
               act.SpawnCommandInNewTab({
                  args = { 'D:\\PowerShell-7.5.1-win-x64\\pwsh.exe', '-NoLogo', '-Command', 'ssh ' .. line }
               }),
               pane
            )
         end
      end)
   })},
   { 
      key = 'c', 
      mods = mod.SUPER_REV, 
      action = wezterm.action_callback(copy_current_working_dir)
   },

   -- ========== 主题和标签页颜色 ========== --
   { 
      key = 'm', 
      mods = mod.SUPER, 
      action = theme_switcher:create_theme_selector()
   },
   { 
      key = 'm', 
      mods = mod.SUPER_REV, 
      action = theme_switcher:cycle_themes()
   },
   -- 标签页颜色预览
   { 
      key = 'p', 
      mods = mod.SUPER, 
      action = tab_colors.create_color_preview()
   },

   -- ========== 🔌 插件快捷键 ========== --
   -- Resurrect 会话管理
   { 
      key = 'r', 
      mods = 'LEADER', 
      action = wezterm.action_callback(function(win, pane)
         local resurrect = wezterm.plugin.require("https://github.com/MLFlexer/resurrect.wezterm")
         resurrect.fuzzy_loader.fuzzy_load(win, pane, function(id, label)
            local type = string.match(id, "^([^/]+)") -- match before '/'
            id = string.match(id, "([^/]+)$") -- match after '/'
            id = string.match(id, "(.+)%..+$") -- remove file extension
            local opts = {
               relative = true,
               restore_text = true,
               on_pane_restore = resurrect.tab_state.default_on_pane_restore,
            }
            if type == "workspace" then
               local state = resurrect.state_manager.load_state(id, "workspace")
               resurrect.workspace_state.restore_workspace(state, opts)
            elseif type == "window" then
               local state = resurrect.state_manager.load_state(id, "window")
               resurrect.window_state.restore_window(pane:window(), state, opts)
            elseif type == "tab" then
               local state = resurrect.state_manager.load_state(id, "tab")
               resurrect.tab_state.restore_tab(pane:tab(), state, opts)
            end
         end)
      end),
   },
   
   -- 保存当前工作区状态
   { 
      key = 's', 
      mods = 'LEADER', 
      action = wezterm.action_callback(function(win, pane)
         local resurrect = wezterm.plugin.require("https://github.com/MLFlexer/resurrect.wezterm")
         resurrect.state_manager.save_state(resurrect.workspace_state.get_workspace_state())
         win:toast_notification('WezTerm', '💾 工作区状态已保存', nil, 2000)
      end),
   },

   -- ========== URL 操作 ========== --
   {
      key = 'u',
      mods = mod.SUPER_REV,
      action = wezterm.action.QuickSelectArgs({
         label = 'open url',
         patterns = {
            '\\((https?://\\S+)\\)',
            '\\[(https?://\\S+)\\]',
            '\\{(https?://\\S+)\\}',
            '<(https?://\\S+)>',
            '\\bhttps?://\\S+[)/a-zA-Z0-9-]+'
         },
         action = wezterm.action_callback(function(window, pane)
            local url = window:get_selection_text_for_pane(pane)
            wezterm.log_info('opening: ' .. url)
            wezterm.open_with(url)
         end),
      }),
   },

   -- ========== 标签页操作 ========== --
   { key = '[', mods = mod.SUPER, action = act.ActivateTabRelative(-1) },
   { key = ']', mods = mod.SUPER, action = act.ActivateTabRelative(1) },
   { key = '[', mods = mod.SUPER_REV, action = act.MoveTabRelative(-1) },
   { key = ']', mods = mod.SUPER_REV, action = act.MoveTabRelative(1) },

   -- 标签页管理（保留有用的功能）
   { key = '0', mods = mod.SUPER, action = act.EmitEvent('tabs.manual-update-tab-title') },
   { key = '9', mods = mod.SUPER, action = act.EmitEvent('tabs.toggle-tab-bar') },

   -- ========== 窗格操作 ========== --
   -- 分割窗格
   { key = [[\]], mods = mod.SUPER, action = act.SplitVertical({ domain = 'CurrentPaneDomain' }) },
   { key = [[\]], mods = mod.SUPER_REV, action = act.SplitHorizontal({ domain = 'CurrentPaneDomain' }) },

   -- 窗格导航 (Vim 风格)
   { key = 'h', mods = mod.SUPER_REV, action = act.ActivatePaneDirection('Left') },
   { key = 'j', mods = mod.SUPER_REV, action = act.ActivatePaneDirection('Down') },
   { key = 'k', mods = mod.SUPER_REV, action = act.ActivatePaneDirection('Up') },
   { key = 'l', mods = mod.SUPER_REV, action = act.ActivatePaneDirection('Right') },

   -- 窗格控制
   { key = 'Enter', mods = mod.SUPER, action = act.TogglePaneZoomState },
   { key = 'p', mods = mod.SUPER_REV, action = act.PaneSelect({ alphabet = '1234567890', mode = 'SwapWithActiveKeepFocus' }) },

   -- 窗格增强功能
   {
      key = 'e',
      mods = mod.SUPER,
      action = wezterm.action_callback(function(window, pane)
         -- 平衡窗格大小的简单实现
         window:perform_action(act.AdjustPaneSize({ 'Left', 1 }), pane)
         window:perform_action(act.AdjustPaneSize({ 'Right', 1 }), pane)
      end)
   },

   -- ========== 背景控制 (解决冲突) ========== --
   { key = [[,]], mods = mod.SUPER, action = wezterm.action_callback(function(window, _pane)
      backdrops:cycle_back(window)
   end)},
   { key = [[.]], mods = mod.SUPER, action = wezterm.action_callback(function(window, _pane)
      backdrops:cycle_forward(window)
   end)},
   { key = [[/]], mods = mod.SUPER, action = wezterm.action_callback(function(window, _pane)
      backdrops:random(window)
   end)},
   -- 使用不同的键来避免冲突
   { key = [[?]], mods = mod.SUPER, action = act.InputSelector({
      title = 'InputSelector: Select Background',
      choices = backdrops:choices(),
      fuzzy = true,
      fuzzy_description = 'Select Background: ',
      action = wezterm.action_callback(function(window, _pane, idx)
         if not idx then
            return
         end
         ---@diagnostic disable-next-line: param-type-mismatch
         backdrops:set_img(window, tonumber(idx))
      end),
   })},

   -- ========== 窗口操作 ========== --
   { key = '-', mods = mod.SUPER, action = wezterm.action_callback(function(window, _pane)
      local dimensions = window:get_dimensions()
      if dimensions.is_full_screen then
         return
      end
      local new_width = dimensions.pixel_width - 50
      local new_height = dimensions.pixel_height - 50
      window:set_inner_size(new_width, new_height)
   end)},
   { key = '=', mods = mod.SUPER, action = wezterm.action_callback(function(window, _pane)
      local dimensions = window:get_dimensions()
      if dimensions.is_full_screen then
         return
      end
      local new_width = dimensions.pixel_width + 50
      local new_height = dimensions.pixel_height + 50
      window:set_inner_size(new_width, new_height)
   end)},

   -- ========== 滚动控制 ========== --
   { key = 'u', mods = mod.SUPER, action = act.ScrollByLine(-5) },
   { key = 'd', mods = mod.SUPER, action = act.ScrollByLine(5) },
   { key = 'PageUp', mods = 'NONE', action = act.ScrollByPage(-0.75) },
   { key = 'PageDown', mods = 'NONE', action = act.ScrollByPage(0.75) },

   -- ========== 复制粘贴 ========== --
   { key = 'c', mods = 'CTRL|SHIFT', action = act.CopyTo('Clipboard') },
   { key = 'v', mods = 'CTRL|SHIFT', action = act.PasteFrom('Clipboard') },

   -- ========== 光标移动 ========== --
   { key = 'LeftArrow', mods = mod.SUPER, action = act.SendString '\u{1b}OH' },
   { key = 'RightArrow', mods = mod.SUPER, action = act.SendString '\u{1b}OF' },
   { key = 'Backspace', mods = mod.SUPER, action = act.SendString '\u{15}' },

   -- ========== Key Tables 激活 ========== --
   { key = 'f', mods = 'LEADER', action = act.ActivateKeyTable({
      name = 'resize_font',
      one_shot = false,
      timeout_milliseconds = 1000,
   })},
   { key = 'p', mods = 'LEADER', action = act.ActivateKeyTable({
      name = 'resize_pane',
      one_shot = false,
      timeout_milliseconds = 1000,
   })},
}

-- Key Tables 定义
local key_tables = {
   resize_font = {
      { key = 'k', action = act.IncreaseFontSize },
      { key = 'j', action = act.DecreaseFontSize },
      { key = 'r', action = act.ResetFontSize },
      { key = 'Escape', action = 'PopKeyTable' },
      { key = 'q', action = 'PopKeyTable' },
   },
   resize_pane = {
      { key = 'k', action = act.AdjustPaneSize({ 'Up', 1 }) },
      { key = 'j', action = act.AdjustPaneSize({ 'Down', 1 }) },
      { key = 'h', action = act.AdjustPaneSize({ 'Left', 1 }) },
      { key = 'l', action = act.AdjustPaneSize({ 'Right', 1 }) },
      { key = 'Escape', action = 'PopKeyTable' },
      { key = 'q', action = 'PopKeyTable' },
   },
}

local mouse_bindings = {
   -- Ctrl-click 打开链接
   {
      event = { Up = { streak = 1, button = 'Left' } },
      mods = 'CTRL',
      action = act.OpenLinkAtMouseCursor,
   },
}

return {
   disable_default_key_bindings = true,
   leader = { key = 'Space', mods = mod.SUPER_REV },
   keys = keys,
   key_tables = key_tables,
   mouse_bindings = mouse_bindings,
}
